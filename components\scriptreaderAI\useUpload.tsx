"use client";

import { getStorage, ref, uploadBytesResumable, getDownloadURL } from "firebase/storage";
import { useState, useCallback } from "react";
import { useSession } from "next-auth/react"; // Using Next-Auth for authentication
import { useRouter } from "next/navigation";
import { doc, setDoc } from "firebase/firestore";
import { db } from "components/firebase"; // Fixed absolute import path for production compatibility

export enum StatusText {
  UPLOADING = "Uploading file...",
  UPLOADED = "File uploaded successfully...",
  PROCESSING = "Processing file...",
  UPLOADING_ELEVENLABS = "Uploading to ElevenLabs...",
  COMPLETED = "Upload and processing complete.",
  ERROR = "An error occurred.",
  GENERATING = "GENERATING",
  SAVING = "SAVING",
  IDLE = "IDLE",
  AUTH_ERROR = "Authentication error. Please sign in again."
}

export type Status = StatusText;

const SUPPORTED_IMAGE_TYPES = [
  "image/jpeg",
  "image/png",
  "image/gif",
  "image/webp",
];

const SUPPORTED_DOCUMENT_TYPES = [
  "application/pdf",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  "text/plain",
  "application/rtf",
  "application/vnd.ms-excel",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  "text/csv",
];

const MAX_IMAGE_SIZE = 4 * 1024 * 1024; // 4MB
const MAX_DOC_SIZE = 50 * 1024 * 1024; // 50MB

interface UploadHookResult {
  progress: number | null;
  status: Status | null;
  handleUpload: (
    file: File,
    category: string | null,
    userId: string,
    docId: string
  ) => Promise<void>;
  error: string | null;
}

function useUpload(): UploadHookResult {
  const [progress, setProgress] = useState<number | null>(null);
  const [status, setStatus] = useState<Status | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { data: session, status: sessionStatus } = useSession();
  const router = useRouter();

  // File validation utility functions
  const isImageFile = (file: File): boolean => SUPPORTED_IMAGE_TYPES.includes(file.type);
  const isDocumentFile = (file: File): boolean => SUPPORTED_DOCUMENT_TYPES.includes(file.type);

  const validateFileSize = (file: File): boolean => {
    return isImageFile(file) ? file.size <= MAX_IMAGE_SIZE : file.size <= MAX_DOC_SIZE;
  };

  const checkImageDimensions = (file: File): Promise<boolean> => {
    return new Promise((resolve) => {
      if (!isImageFile(file)) {
        resolve(true);
        return;
      }
      const img = new Image();
      const objectUrl = URL.createObjectURL(file);
      img.onload = () => {
        URL.revokeObjectURL(objectUrl);
        const pixels = img.width * img.height;
        const MAX_IMAGE_PIXELS = 33177600;
        if (pixels > MAX_IMAGE_PIXELS) {
          setError(`Image dimensions too large. Maximum allowed pixels is ${MAX_IMAGE_PIXELS.toLocaleString()}. Your image has ${pixels.toLocaleString()} pixels (${img.width}x${img.height}).`);
          resolve(false);
        } else {
          resolve(true);
        }
      };
      img.onerror = () => {
        URL.revokeObjectURL(objectUrl);
        setError("Failed to read image dimensions");
        resolve(false);
      };
      img.src = objectUrl;
    });
  };

  const handleUpload = useCallback(
    async (
      file: File,
      category: string | null,
      userId: string,
      docId: string
    ) => {
      // Reset state for new upload
      setError(null);
      setProgress(null);
      setStatus(null);

      // Authentication check using Next-Auth session
      if (sessionStatus !== "authenticated" || !session) {
        console.error("Authentication required to upload files");
        setError("Authentication required. Please sign in again.");
        setStatus(StatusText.AUTH_ERROR);
        return;
      }

      // Validate input parameters
      if (!file) {
        console.error("No file selected for upload");
        setError("No file selected for upload.");
        setStatus(StatusText.ERROR);
        return;
      }

      if (!userId || !docId) {
        console.error("Missing required parameters: userId or docId");
        setError("Missing user ID or document ID.");
        setStatus(StatusText.ERROR);
        return;
      }

      // Validate file type
      if (!isImageFile(file) && !isDocumentFile(file)) {
        console.error(`Unsupported file type: ${file.type}`);
        setError(`Unsupported file type: ${file.type}\n\nSupported types are:\nImages: ${SUPPORTED_IMAGE_TYPES.join(', ')}\nDocuments: ${SUPPORTED_DOCUMENT_TYPES.join(', ')}`);
        setStatus(StatusText.ERROR);
        return;
      }

      // Validate file size
      if (!validateFileSize(file)) {
        const maxSize = isImageFile(file) ? "4MB" : "50MB";
        console.error(`File size exceeds the ${maxSize} limit`);
        setError(`File size exceeds the ${maxSize} limit`);
        setStatus(StatusText.ERROR);
        return;
      }

      // Validate image dimensions if applicable
      const validDimensions = await checkImageDimensions(file);
      if (!validDimensions) {
        console.error("Image dimensions validation failed");
        setStatus(StatusText.ERROR);
        return;
      }

      // Begin upload process - this is where UI should show loading indicator
      setProgress(0);
      setStatus(StatusText.UPLOADING);

      try {
        const resolvedCategory = category || "SceneMate";
        const storage = getStorage();
        const storageRef = ref(storage, `uploads/${userId}/${docId}`);
        const uploadTask = uploadBytesResumable(storageRef, file);

        // Track upload progress
        uploadTask.on(
          "state_changed",
          (snapshot) => {
            const percent = Math.round((snapshot.bytesTransferred / snapshot.totalBytes) * 100);
            setProgress(percent);
            console.log(`Upload progress: ${percent}%`);
          },
          (error) => {
            console.error("Error uploading file to storage:", error);
            setError(`Error uploading file: ${error.message}`);
            setStatus(StatusText.ERROR);
            setProgress(null);
          },
          async () => {
            // Upload completed successfully
            setStatus(StatusText.UPLOADED);
            console.log("File uploaded to storage successfully");
            
            try {
              const downloadUrl = await getDownloadURL(uploadTask.snapshot.ref);
              console.log("Download URL obtained:", downloadUrl);

              // Store file metadata in Firestore
              await setDoc(doc(db, "users", userId, "files", docId), {
                name: file.name,
                size: file.size,
                type: file.type,
                category: resolvedCategory,
                namespace: docId,
                downloadUrl: downloadUrl,
                ref: uploadTask.snapshot.ref.fullPath,
                createdAt: new Date(),
                isImage: isImageFile(file),
                elevenlabs_upload_requested: true, // Preserve ElevenLabs flag from v2
              });
              console.log("File metadata stored in Firestore");

              // Process the uploaded file with backend API
              setStatus(StatusText.PROCESSING);
              console.log("Beginning file processing with API");

              // Use exclusively the processScriptfile endpoint
              const response = await fetch("/api/processScriptfile", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  // Next-Auth handles authentication via cookies automatically
                },
                body: JSON.stringify({
                  docId: docId,
                  userId,
                  category: resolvedCategory,
                  fileName: file.name,
                  fileType: file.type,
                  fileUrl: downloadUrl,
                  isImage: isImageFile(file),
                  uploadToElevenLabs: true, // Always enable ElevenLabs upload
                }),
              });

              if (!response.ok) {
                let errorMessage = "Failed to process the uploaded file.";
                try {
                  const errorData = await response.json();
                  errorMessage = errorData.error || `Server error: ${response.status}`;
                } catch (e) {
                  console.error("Error parsing error response:", e);
                }
                throw new Error(errorMessage);
              }

              // Handle ElevenLabs integration data from response
              try {
                const responseData = await response.json();
                console.log("API response received:", responseData);
                
                if (responseData.elevenLabs) {
                  if (responseData.elevenLabs.error) {
                    console.warn("ElevenLabs upload warning:", responseData.elevenLabs.error);
                  } else {
                    console.log("Successfully uploaded to ElevenLabs Knowledge Base:", responseData.elevenLabs);
                  }
                }
              } catch (parseError) {
                console.warn("Could not parse API response:", parseError);
              }

              // Complete the process
              setStatus(StatusText.COMPLETED);
              console.log("File upload and processing completed successfully");
              
              // Navigate to file manager after successful upload
              // if (process.env.NEXT_PUBLIC_REDIRECT_AFTER_UPLOAD !== 'false') {
              //   router.push("/fileManager");
              // }
            } catch (postUploadError: any) {
              console.error("Error in post-upload processing:", postUploadError);
              setError(postUploadError.message || "Error processing uploaded file");
              setStatus(StatusText.ERROR);
              setProgress(null);
            }
          }
        );
      } catch (error: any) {
        console.error("Fatal error during file upload or processing:", error);
        setError(error.message || "Error during file upload or processing");
        setStatus(StatusText.ERROR);
        setProgress(null);
      }
    },
    [session, sessionStatus, router]
  );

  return { progress, status, handleUpload, error };
}

export default useUpload;