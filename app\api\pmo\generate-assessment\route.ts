import { NextResponse } from 'next/server';
import { PMOAssessmentAgent } from '../../../../lib/agents/pmo/PMOAssessmentAgent';
import { queryDocumentsAgent } from '../../../../components/Agents/QueryDocumentsAgent';
import { PMOFormInput, AgenticTeamId, ModelProvider } from '../../../../lib/agents/pmo/PMOInterfaces';
import { createLlmService } from '../../../../lib/tools/llmServiceAdapter';
//Add table chart tool from llm

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const {
      userId,
      title,
      description,
      priority,
      category,
      contextOptions,
      modelProvider, // Extracted from body
      modelName,     // Extracted from body
    } = body;

    // Modified: Added modelProvider and modelName to the required fields check
    if (!userId || !title || !description || !modelProvider || !modelName) {
      return NextResponse.json(
        { error: 'Missing required fields (userId, title, description, modelProvider, modelName)' },
        { status: 400 }
      );
    }

    // Validate modelProvider to ensure it's a valid ModelProvider type
    const validModelProvider = (modelProvider && ['openai', 'anthropic', 'groq', 'google'].includes(modelProvider))
      ? modelProvider as ModelProvider
      : 'openai'; // Default to 'openai' if invalid or not provided

    // Create an LLM service adapter with the validated model provider
    const llmService = createLlmService(validModelProvider);

    const pmoAssessmentAgent = new PMOAssessmentAgent(
      {
        userId: userId,
        includeExplanation: true,
        streamResponse: false // Streaming from API route is more complex, keep false for now
      },
      llmService // Pass the LLM service as the second parameter
    );

    // Arm the agent with necessary tools
    pmoAssessmentAgent.setQueryDocumentsAgent(queryDocumentsAgent);

    const pmoInput: PMOFormInput = {
      title,
      description,
      priority: priority || 'Medium',
      category: category || 'PMO Request',
      contextOptions: {
        customContext: contextOptions?.customContext || undefined,
        fileIds: contextOptions?.fileIds || [],
        categoryIds: contextOptions?.categoryIds || [],
      },
      // These are part of the input to generateAssessment,
      // but the agent itself also needs configuration for its own LLM.
      // Validate modelProvider to ensure it's a valid ModelProvider type
      modelProvider: (modelProvider && ['openai', 'anthropic', 'groq', 'google'].includes(modelProvider))
        ? modelProvider as ModelProvider
        : 'openai', // Default to 'openai' if invalid or not provided
      modelName: modelName || 'gpt-4o', // Provide a default model name if not specified
    };

    // First, determine the teams based on keywords in the description
    let selectedTeams: AgenticTeamId[] = [];

    // Simple keyword-based team selection
    const descriptionLower = description.toLowerCase();
    if (descriptionLower.includes('market') || descriptionLower.includes('brand') || descriptionLower.includes('campaign')) {
      selectedTeams.push(AgenticTeamId.Marketing);
    } else if (descriptionLower.includes('research') || descriptionLower.includes('analysis') || descriptionLower.includes('data')) {
      selectedTeams.push(AgenticTeamId.Research);
    } else if (descriptionLower.includes('software') || descriptionLower.includes('develop') || descriptionLower.includes('code') || descriptionLower.includes('app')) {
      selectedTeams.push(AgenticTeamId.SoftwareDesign);
    } else if (descriptionLower.includes('sales') || descriptionLower.includes('client') || descriptionLower.includes('revenue')) {
      selectedTeams.push(AgenticTeamId.Sales);
    } else if (descriptionLower.includes('business') || descriptionLower.includes('process') || descriptionLower.includes('strategy')) {
      selectedTeams.push(AgenticTeamId.BusinessAnalysis);
    } else {
      // Default to Research if no keywords match
      selectedTeams.push(AgenticTeamId.Research);
    }

    // Generate a simple assessment for team selection
    const simpleAssessment = `Project Title: ${title}\nDescription: ${description}\nPriority: ${priority || 'Medium'}\nCategory: ${category || 'Unknown'}`;

    // Get context chunks
    let contextChunks: any[] = [];

    // Process context options to get context chunks
    if (contextOptions) {
      if (contextOptions.customContext) {
        // For custom context, create a synthetic chunk
        contextChunks = [{
          content: contextOptions.customContext,
          metadata: {
            fileName: 'Custom Context',
            source: 'User Input',
            relevance: 1.0
          },
          text: contextOptions.customContext
        }];
      } else if (contextOptions.fileIds && contextOptions.fileIds.length > 0) {
        // Query documents by file IDs
        const queryResult = await queryDocumentsAgent.queryDocuments({
          query: description,
          fileIds: contextOptions.fileIds,
          maxResults: 5,
          userId: userId
        });

        if (queryResult.success) {
          contextChunks = queryResult.chunks || [];

          // If no chunks were returned but we have results, create synthetic chunks
          if (contextChunks.length === 0 && queryResult.results.length > 0) {
            contextChunks = queryResult.results.map((result, index) => ({
              content: result,
              metadata: {
                fileName: `Result ${index + 1}`,
                source: contextOptions.fileIds[0] || 'Unknown',
                relevance: 1.0 - (index * 0.1) // Decreasing relevance for each result
              },
              text: result
            }));
          }
        }
      } else if (contextOptions.categoryIds && contextOptions.categoryIds.length > 0) {
        // Query documents by category IDs
        const queryResult = await queryDocumentsAgent.queryDocumentsByCategory({
          query: description,
          categoryIds: contextOptions.categoryIds,
          maxResults: 5,
          userId: userId
        });

        if (queryResult.success) {
          contextChunks = queryResult.chunks || [];

          // If no chunks were returned but we have results, create synthetic chunks
          if (contextChunks.length === 0 && queryResult.results.length > 0) {
            contextChunks = queryResult.results.map((result, index) => ({
              content: result,
              metadata: {
                fileName: `Result ${index + 1}`,
                source: contextOptions.categoryIds[0] || 'Unknown',
                relevance: 1.0 - (index * 0.1) // Decreasing relevance for each result
              },
              text: result
            }));
          }
        }
      }
    }

    // Ensure we always have at least one context chunk
    if (contextChunks.length === 0) {
      const defaultContent = description;
      contextChunks = [{
        content: defaultContent,
        metadata: {
          fileName: 'Project Description',
          source: 'User Input',
          relevance: 1.0
        },
        text: defaultContent
      }];
    }

    // Now generate the requirements specification directly
    const requirementsSpec = await pmoAssessmentAgent.generateRequirementsSpecificationDirectly(
      pmoInput,
      simpleAssessment,
      selectedTeams,
      contextChunks
    );

    // Generate a rationale for team selection
    let teamSelectionRationale = '';
    if (selectedTeams.includes(AgenticTeamId.Marketing)) {
      teamSelectionRationale = "The Marketing Team is best suited for this task as it involves marketing strategy, content creation, brand management, and market analysis.";
    } else if (selectedTeams.includes(AgenticTeamId.Research)) {
      teamSelectionRationale = "The Research Team is best suited for this task as it involves data collection, analysis, and producing research reports.";
    } else if (selectedTeams.includes(AgenticTeamId.SoftwareDesign)) {
      teamSelectionRationale = "The Software Design Team is best suited for this task as it involves software development, UI/UX design, coding, and technical implementation.";
    } else if (selectedTeams.includes(AgenticTeamId.Sales)) {
      teamSelectionRationale = "The Sales Team is best suited for this task as it involves sales strategies, client relationships, and revenue generation.";
    } else if (selectedTeams.includes(AgenticTeamId.BusinessAnalysis)) {
      teamSelectionRationale = "The Business Analysis Team is best suited for this task as it involves business process analysis, requirements gathering, and strategic planning.";
    }

    // Construct the text to be displayed in the UI
    let fullAssessmentText = `## Requirements Specification for: ${pmoInput.title}\n\n${requirementsSpec}`;

    // Add team selection information
    if (selectedTeams.length > 0) {
      const teamNames = selectedTeams.map(teamId => {
        // Get the team name from the enum value
        const getTeamName = (id: AgenticTeamId): string => {
          switch(id) {
            case AgenticTeamId.Marketing: return "Marketing";
            case AgenticTeamId.Research: return "Research";
            case AgenticTeamId.SoftwareDesign: return "Software Design";
            case AgenticTeamId.Sales: return "Sales";
            case AgenticTeamId.BusinessAnalysis: return "Business Analysis";
            default:
              // Attempt to get string representation of enum key if it's a less common/new one
              const enumKey = Object.keys(AgenticTeamId).find(key => (AgenticTeamId as any)[key] === id);
              return enumKey || String(id);
          }
        };
        return getTeamName(teamId);
      }).join(', ');

      fullAssessmentText += `\n\n### Proposed Team Delegation\n**Teams:** ${teamNames}\n**Rationale:** ${teamSelectionRationale}`;
    }

    return NextResponse.json({
      success: true,
      assessmentText: fullAssessmentText,
      selectedTeams: selectedTeams,
      requirementsSpecification: requirementsSpec
    });

  } catch (error: any) {
    console.error('API - Error generating PMO assessment:', error);
    return NextResponse.json({
      error: `Failed to generate assessment: ${error.message}`
    }, { status: 500 });
  }
}