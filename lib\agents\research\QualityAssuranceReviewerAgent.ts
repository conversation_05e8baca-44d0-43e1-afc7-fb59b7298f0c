// research/QualityAssuranceReviewerAgent.ts

import { ResearchAgent } from './ResearchAgent';
import { LlmProvider } from '../../tools/llm-tool';
import { ReportDraft, ResearchTaskBrief, QaChecklistItem, ReviewFeedback } from './ResearchInterfaces';

export class QualityAssuranceReviewerAgent extends ResearchAgent {
    constructor(
        id: string = 'qa-reviewer',
        name: string = 'Quality Assurance Reviewer',
        userId: string = '',
        defaultLlmProvider: LlmProvider = 'openai',
        defaultLlmModel: string = 'gpt-4o'
    ) {
        const role = 'Quality Assurance and Review Specialist';
        const description = `I review draft research outputs for accuracy, completeness against the brief, clarity, objectivity, and proper citation. I provide feedback for revisions or approve the report.`;
        super(id, name, role, description, userId, defaultLlmProvider, defaultLlmModel);
    }

    /**
     * Review a draft report against the brief and quality standards
     * @param draft - The report draft to review
     * @param brief - The original research brief for context
     * @returns Review feedback
     */
    async reviewReport(draft: ReportDraft, brief: ResearchTaskBrief): Promise<ReviewFeedback> {
        console.log(`[${this.name}] Reviewing draft ${draft.draftId} for task ${brief.taskId}`);

        // Define QA checks
        const checks = [
            `Does the report accurately reflect the topic "${brief.topic}"?`,
            `Does the report address the key questions: ${brief.keyQuestions.join(', ')}?`,
            `Is the report scope consistent with: "${brief.clarifiedScope}"?`,
            `Is the language clear, concise, and objective?`,
            `Is the structure logical and easy to follow?`,
            `Are claims generally supported (based on context provided in draft)?`, // Basic check
            `Are sources cited/listed in a bibliography?`,
            `Does the format match the required "${brief.outputFormat || 'report'}"?`
        ];

        const prompt = `
          You are a Quality Assurance Reviewer. Review the following draft report against the original research brief.
          Evaluate based on accuracy, completeness, clarity, objectivity, structure, and citation presence.

          Original Brief:
          Topic: ${brief.topic}
          Scope: ${brief.clarifiedScope}
          Key Questions: ${brief.keyQuestions.join(', ')}
          Required Format: ${brief.outputFormat || 'report'}

          Draft Report Content (Excerpt):
          Title: ${draft.title}
          ${draft.content.substring(0, 4000)}...
          ${draft.bibliography ? `\nBibliography URLs: ${draft.bibliography.slice(0, 5).join(', ')}...` : ''}

          For each of the following checks, provide a 'pass' or 'fail' status and brief comments if 'fail':
          ${checks.map((c, i) => `${i + 1}. ${c}`).join('\n')}

          Finally, provide an overall status ('approved', 'needs_revision', 'rejected') and a summary feedback paragraph.

          Format your response as a JSON object with keys:
          - "checklist": An array of objects, each with "check", "status" ('pass'/'fail'), "comments".
          - "overallStatus": 'approved', 'needs_revision', or 'rejected'.
          - "summaryFeedback": A summary paragraph.
        `;

        const responseJson = await this.processRequest(prompt);
        let feedback: ReviewFeedback;

        try {
            const parsed = JSON.parse(responseJson);

            const checklist: QaChecklistItem[] = (parsed.checklist || []).map((item: any, index: number) => ({
                check: checks[index] || item.check || 'Unknown Check',
                status: item.status === 'pass' ? 'pass' : 'fail', // Default to fail if not explicit pass
                comments: item.comments || '',
            }));

            // Ensure all checks are represented
            if (checklist.length < checks.length) {
                for (let i = checklist.length; i < checks.length; i++) {
                    checklist.push({ check: checks[i], status: 'fail', comments: 'Reviewer did not evaluate.' });
                }
            }


            feedback = {
                reviewId: `rev-${draft.draftId}-${Date.now()}`,
                draftId: draft.draftId,
                parentTaskId: brief.taskId, // Use the main task ID
                overallStatus: parsed.overallStatus || 'needs_revision',
                checklist: checklist,
                summaryFeedback: parsed.summaryFeedback || 'No summary feedback provided.',
                reviewerAgentId: this.id,
            };
             console.log(`[${this.name}] Review complete for ${draft.draftId}. Status: ${feedback.overallStatus}`);

        } catch (error) {
            console.error(`[${this.name}] Failed to parse review response for ${draft.draftId}:`, error);
            feedback = { // Provide fallback structure
                reviewId: `rev-${draft.draftId}-${Date.now()}-error`,
                draftId: draft.draftId, parentTaskId: brief.taskId,
                overallStatus: 'needs_revision',
                checklist: checks.map(c => ({ check: c, status: 'fail', comments: 'Error during review process.' })),
                summaryFeedback: 'Failed to complete review due to processing error.',
                reviewerAgentId: this.id,
            };
        }
        return feedback;
    }

    /**
     * Handles incoming tasks/messages for quality assurance review
     */
    async handleTask(messageContent: string, metadata: Record<string, any>): Promise<void> {
        if (metadata.subTaskId && metadata.instruction && metadata.draft && metadata.brief) {
             console.log(`[${this.name}] Received task: ${metadata.subTaskId} - ${metadata.instruction}`);
             const draft: ReportDraft = metadata.draft;
             const brief: ResearchTaskBrief = metadata.brief; // Need the brief for context

             const feedback = await this.reviewReport(draft, brief);

             // Send feedback back to the Lead
             const leadAgentId = 'research-lead'; // Placeholder
             await this.sendMessage(
                 leadAgentId,
                 `Review complete for draft ${draft.draftId}. Status: ${feedback.overallStatus}`,
                 {
                     type: 'review_complete',
                     subTaskId: metadata.subTaskId,
                     feedback: feedback // Attach feedback
                 }
             );
              await this.updateTaskStatus(metadata.taskId || metadata.subTaskId, 'completed');

        } else {
             console.warn(`[${this.name}] Received message missing required data for review:`, metadata);
        }
    }
}