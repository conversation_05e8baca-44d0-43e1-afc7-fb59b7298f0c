import { NextRequest, NextResponse } from 'next/server';
import { adminDb } from 'components/firebase-admin';

export async function GET(
  req: NextRequest,
  { params }: { params: { jobId: string } }
) {
  try {
    const jobId = params.jobId;
    
    // Fetch job status from Firestore
    const jobDoc = await adminDb.collection('marketingJobs').doc(jobId).get();
    
    if (!jobDoc.exists) {
      return NextResponse.json({ error: 'Job not found' }, { status: 404 });
    }
    
    const jobData = jobDoc.data();
    
    // Check if jobData is undefined
    if (!jobData) {
      return NextResponse.json({ error: 'Job data is empty' }, { status: 500 });
    }
    
    return NextResponse.json({
      jobId,
      status: jobData.status || 'processing',
      progress: jobData.progress || 0,
      result: jobData.status === 'completed' ? jobData.result : null,
      error: jobData.error || null
    });
  } catch (error) {
    console.error('Error fetching job status:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Failed to fetch job status' 
    }, { status: 500 });
  }
}
