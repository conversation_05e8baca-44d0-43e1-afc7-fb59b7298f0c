/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  // The appDir option is no longer needed as it's the default in newer Next.js versions
  // Configure allowed image domains
  images: {
    domains: [
      'lh3.googleusercontent.com',     // Google profile images
      'firebasestorage.googleapis.com', // Firebase Storage images
      'googleusercontent.com',          // Other Google image domains
      'googleapis.com'                  // Google APIs
    ],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.googleusercontent.com',
        pathname: '**',
      }
    ],
  },
  // Enable WebAssembly support
  webpack: (config) => {
    // Enable both sync and async WebAssembly
    config.experiments = {
      ...config.experiments,
      asyncWebAssembly: true,
      syncWebAssembly: true,
    };

    // Add rule for WebAssembly files
    config.module.rules.push({
      test: /\.wasm$/,
      type: 'webassembly/async',
    });

    return config;
  },
  // Ensure that Next.js recognizes our custom routes
  async rewrites() {
    return [
      {
        source: '/services/admin',
        destination: '/services/admin',
      },
      {
        source: '/services/admin/login',
        destination: '/services/admin/login',
      },
      {
        source: '/services/admin/planner',
        destination: '/services/admin/planner',
      },
      {
        source: '/services/admin/planner/:projectId',
        destination: '/services/admin/planner/:projectId',
      },
      {
        source: '/services/admin/marketing-access',
        destination: '/services/admin/marketing-access',
      },
      {
        source: '/marketing-agent-tests/login',
        destination: '/marketing-agent-tests/login',
      },
      {
        source: '/services/pmo',
        destination: '/services/pmo',
      },
      {
        source: '/services/pmo/records/:recordId',
        destination: '/services/pmo/records/:recordId',
      },
      // Add routes for other pages
      {
        source: '/',
        destination: '/page',
      },
      {
        source: '/scriptReader',
        destination: '/scriptReader',
      },
    ];
  },
};

module.exports = nextConfig;
