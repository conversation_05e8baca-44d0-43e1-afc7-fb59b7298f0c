/**
 * API endpoint for previewing project creation from Strategic Director Agent outputs
 *
 * This endpoint:
 * 1. Receives a request with requestId (Agent_Output document ID) and optional pmoId
 * 2. Uses CreateProjectAgent to extract single project from PMO title
 * 3. Uses pmoProjectsTaskAgent to extract tasks with subtasks
 * 4. Returns the project and tasks data for review modal (does NOT create anything)
 */

import { NextRequest, NextResponse } from 'next/server';
import { createProjectAgent } from '../../../lib/agents/createProjectAgent';
import { pmoProjectsTaskAgent } from '../../../lib/agents/pmoProjectsTaskAgent';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]/authOptions';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { requestId, pmoId, userId } = body;

    // Validate required parameters
    if (!requestId) {
      return NextResponse.json(
        { error: 'Missing required parameter: requestId' },
        { status: 400 }
      );
    }

    // Use the authenticated user's email or provided userId
    const effectiveUserId = userId || session.user.email;

    console.log(`API: project-creation-preview - Processing request for requestId: ${requestId}, userId: ${effectiveUserId}, pmoId: ${pmoId || 'none'}`);

    // Get agent output
    const agentOutput = await createProjectAgent._getAgentOutput(requestId);
    if (!agentOutput) {
      return NextResponse.json(
        { error: 'Agent output not found for the provided requestId' },
        { status: 404 }
      );
    }

    // Get PMO title
    const pmoTitle = await createProjectAgent._getPMOTitle(agentOutput, pmoId, effectiveUserId);
    if (!pmoTitle) {
      return NextResponse.json(
        { error: 'Could not determine PMO title for project creation' },
        { status: 400 }
      );
    }

    // Create project directly from PMO data (no LLM extraction needed)
    const extractedProjects = await createProjectAgent._createProjectFromPMOData(agentOutput, pmoTitle, pmoId, effectiveUserId);

    if (!extractedProjects.projects || extractedProjects.projects.length === 0) {
      return NextResponse.json(
        {
          error: 'No project could be created from PMO data',
          analysis: extractedProjects.analysis
        },
        { status: 400 }
      );
    }

    const project = extractedProjects.projects[0]; // Should only be one project

    // Extract tasks using pmoProjectsTaskAgent
    const taskResult = await pmoProjectsTaskAgent.extractTasksFromAgentOutput(
      requestId,
      project.projectName,
      project.projectDescription
    );

    if (!taskResult.success) {
      return NextResponse.json(
        {
          error: 'Failed to extract tasks from agent output',
          details: taskResult.error
        },
        { status: 500 }
      );
    }

    // Format tasks for the review modal
    const formattedTasks = taskResult.tasksCreated.map((task, index) => ({
      id: `task_${index}`,
      title: task.title,
      description: task.description,
      category: task.category,
      priority: task.priority as 'High' | 'Medium' | 'Low',
      dueDate: task.dueDate.toISOString().split('T')[0],
      assignedTo: task.assignedTo,
      teamAssignment: task.teamAssignment,
      subtasks: task.subtasks?.map((subtask, subIndex) => ({
        id: `subtask_${index}_${subIndex}`,
        title: subtask.title,
        description: subtask.description,
        priority: subtask.priority as 'High' | 'Medium' | 'Low',
        dueDate: subtask.dueDate?.toISOString().split('T')[0],
        approved: true // Default to approved
      })) || [],
      approved: true // Default to approved
    }));

    // Format project for the review modal
    const formattedProject = {
      projectName: project.projectName,
      projectDescription: project.projectDescription,
      startDate: project.startDate,
      endDate: project.endDate,
      categories: project.categories,
      priority: project.priority as 'High' | 'Medium' | 'Low',
      estimatedDuration: project.estimatedDuration || 'Not specified'
    };

    console.log(`API: project-creation-preview - Successfully extracted project "${project.projectName}" with ${formattedTasks.length} tasks`);

    return NextResponse.json({
      success: true,
      data: {
        project: formattedProject,
        tasks: formattedTasks,
        analysis: extractedProjects.analysis,
        requestId,
        pmoId,
        userId: effectiveUserId
      }
    });

  } catch (error: any) {
    console.error('API: project-creation-preview - Error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error.message
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  // Return API documentation
  return NextResponse.json({
    endpoint: '/api/project-creation-preview',
    description: 'Previews project and tasks creation from Strategic Director Agent outputs for review',
    methods: ['POST'],
    parameters: {
      requestId: {
        type: 'string',
        required: true,
        description: 'The ID of the Agent_Output document in Firebase'
      },
      pmoId: {
        type: 'string',
        required: false,
        description: 'Optional PMO record ID to get PMO title'
      },
      userId: {
        type: 'string',
        required: false,
        description: 'User ID to assign as project owner (defaults to authenticated user)'
      }
    },
    response: {
      success: 'boolean',
      data: {
        project: 'ProjectData object with project details',
        tasks: 'Array of TaskData objects with subtasks',
        analysis: 'AI analysis of the extraction',
        requestId: 'string',
        pmoId: 'string',
        userId: 'string'
      }
    },
    example: {
      requestId: 'agent_output_document_id',
      pmoId: 'optional_pmo_record_id',
      userId: 'optional_user_email'
    }
  });
}
