/**
 * Example of using the image generation tools with Firebase
 */

import { generateImageTool } from '../lib/tools/generate-image';
import { generateWithImagen } from '../lib/tools/imagen';
import {
  generateAndStoreImage,
  saveImageToGallery,
  processFileImage
} from '../lib/tools/firebase-image-integration';

/**
 * Example of generating an image with Firebase integration
 */
async function generateImageExample() {
  try {
    // User ID for Firebase storage
    const userId = 'example-user-123';

    // Prompt for image generation
    const prompt = 'A beautiful mountain landscape at sunset with a lake reflecting the sky';

    // Method 1: Using the generateImageTool with Firebase integration
    console.log('Method 1: Using generateImageTool with Firebase integration');
    const result1 = await generateImageTool.generateImage({
      prompt,
      userId,
      model: 'gpt-image-1',
      size: '1024x1024'
    });

    console.log('Image generated successfully:');
    console.log(`- Image URL: ${result1.imageUrl}`);
    console.log(`- Job ID: ${result1.jobId}`);
    console.log(`- Namespace: ${result1.namespace}`);

    // Method 2: Using the convenience function for Firebase integration
    console.log('\nMethod 2: Using convenience function for Firebase integration');
    const result2 = await generateAndStoreImage(prompt, userId, 'gpt-image-1');

    if (result2.success) {
      console.log('Image generated and stored successfully:');
      console.log(`- Image URL: ${result2.imageUrl}`);
      console.log(`- Job ID: ${result2.jobId}`);
      console.log(`- Namespace: ${result2.namespace}`);

      // Save the image to the user's gallery
      console.log('\nSaving image to gallery...');
      const galleryResult = await saveImageToGallery(
        result2.imageUrl!,
        prompt,
        userId,
        result2.jobId!,
        'gpt-image-1',
        result2.namespace // Pass the namespace
      );

      if (galleryResult.success) {
        console.log('Image saved to gallery successfully:');
        console.log(`- Namespace: ${galleryResult.namespace}`);

        // Process the file image for ByteStore indexing
        console.log('\nProcessing file image for ByteStore indexing...');
        const processResult = await processFileImage(
          galleryResult.namespace!,
          userId,
          `generated-image-${result2.jobId}.png`,
          result2.imageUrl!
        );

        if (processResult.success) {
          console.log('File image processed successfully');
        } else {
          console.error(`Failed to process file image: ${processResult.error}`);
        }
      } else {
        console.error(`Failed to save image to gallery: ${galleryResult.error}`);
      }
    } else {
      console.error(`Failed to generate and store image: ${result2.error}`);
    }

    // Method 3: Using Google's Imagen with Firebase integration
    console.log('\nMethod 3: Using Google\'s Imagen with Firebase integration');
    const result3 = await generateWithImagen({
      prompt,
      userId,
      model: 'imagen-3.0-generate-002',
      numberOfImages: 1
    });

    console.log('Imagen image generated successfully:');
    console.log(`- Image URL: ${result3.imageUrl}`);
    console.log(`- Job ID: ${result3.jobId}`);
    console.log(`- Namespace: ${result3.namespace}`);

    // Save the Imagen image to the user's gallery
    console.log('\nSaving Imagen image to gallery...');
    const imagenGalleryResult = await saveImageToGallery(
      result3.imageUrl,
      prompt,
      userId,
      result3.jobId,
      'imagen-3.0-generate-002',
      result3.namespace // Pass the namespace from Imagen
    );

    if (imagenGalleryResult.success) {
      console.log('Imagen image saved to gallery successfully:');
      console.log(`- Namespace: ${imagenGalleryResult.namespace}`);
    } else {
      console.error(`Failed to save Imagen image to gallery: ${imagenGalleryResult.error}`);
    }

  } catch (error: any) {
    console.error('Error in image generation example:', error);
  }
}

// Run the example
generateImageExample().then(() => {
  console.log('Example completed');
}).catch((error) => {
  console.error('Example failed:', error);
});
