import { NextRequest, NextResponse } from 'next/server';
import { adminDb } from '../../../../components/firebase/admin';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/authOptions';

/**
 * API endpoint to fetch global agent outputs
 * This endpoint is used by client-side code to access the global Agent_Output collection
 * without importing Firebase Admin SDK directly in the browser
 */
export async function GET(req: NextRequest) {
  try {
    // Get the user's session for authentication
    const session = await getServerSession(authOptions);
    
    // Extract query parameters
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');
    const agentType = searchParams.get('agentType');

    // Validate authentication and user ID
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!userId) {
      return NextResponse.json({ error: 'userId parameter is required' }, { status: 400 });
    }

    // Ensure the user can only access their own data
    if (session.user.email !== userId) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Check if admin DB is available
    if (!adminDb) {
      return NextResponse.json({ 
        success: false, 
        error: 'Admin database not available',
        outputs: []
      });
    }

    // Create a reference to the global Agent_Output collection
    const agentOutputsRef = adminDb.collection('Agent_Output');

    // Create a query - filter by userId first
    let q = agentOutputsRef.where('userId', '==', userId);

    // Add agent type filter if provided
    if (agentType) {
      q = q.where('agentType', '==', agentType);
    }

    // Order by timestamp descending and limit results
    q = q.orderBy('timestamp', 'desc').limit(100);

    // Get the documents
    const outputDocs = await q.get();

    // Map the documents to agent outputs
    const outputs = outputDocs.docs.map(doc => {
      const data = doc.data();

      // Convert Firestore timestamps to ISO strings
      const createdAt = data.timestamp?.toDate()?.toISOString() || new Date().toISOString();
      const updatedAt = createdAt;

      // Extract title and content from the result structure
      const title = data.prompt?.substring(0, 100) || 'Agent Output';
      const content = data.result?.output || data.result?.message || '';

      return {
        id: doc.id,
        agentType: data.agentType || '',
        title,
        content,
        fileUrl: data.result?.documentUrl || undefined,
        createdAt,
        updatedAt,
        metadata: data.metadata || {},
        // Include additional fields from global collection
        pmoMetadata: data.pmoMetadata || undefined,
        category: data.category || undefined,
        contextOptions: data.contextOptions || undefined
      };
    });

    return NextResponse.json({
      success: true,
      outputs
    });

  } catch (error) {
    console.error('Error fetching global agent outputs:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      outputs: []
    }, { status: 500 });
  }
}
