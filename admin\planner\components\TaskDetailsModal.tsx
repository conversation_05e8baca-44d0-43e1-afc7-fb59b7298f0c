'use client';

import React from 'react';
import { Task } from '../types';
import { X, Edit } from 'lucide-react';
import { format } from 'date-fns';

interface TaskDetailsModalProps {
  task: Task;
  onClose: () => void;
  onEdit?: (task: Task) => void;
}

const TaskDetailsModal: React.FC<TaskDetailsModalProps> = ({
  task,
  onClose,
  onEdit
}) => {
  // Function to get priority color
  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case 'Critical': return 'bg-red-900/50 text-red-300';
      case 'High': return 'bg-orange-900/50 text-orange-300';
      case 'Medium': return 'bg-yellow-900/50 text-yellow-300';
      case 'Low': return 'bg-green-900/50 text-green-300';
      default: return 'bg-gray-700 text-gray-300';
    }
  };

  // Function to get status color
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'Not Started': return 'bg-gray-700 text-gray-300';
      case 'In Progress': return 'bg-blue-900/50 text-blue-300';
      case 'Blocked': return 'bg-red-900/50 text-red-300';
      case 'Complete': return 'bg-green-900/50 text-green-300';
      default: return 'bg-gray-700 text-gray-300';
    }
  };

  // Format date for display
  const formatDate = (dateString: string | Date): string => {
    if (!dateString) return 'Not set';
    return format(new Date(dateString), 'MMM d, yyyy');
  };

  // Handle edit button click
  const handleEdit = () => {
    if (onEdit) {
      onEdit(task);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-start mb-4">
          <h3 className="text-xl font-bold text-white">{task.title}</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <p className="text-gray-400 text-sm mb-1">Status</p>
            <div className={`px-3 py-1 rounded-full text-sm inline-block ${getStatusColor(task.status)}`}>
              {task.status}
            </div>
          </div>
          <div>
            <p className="text-gray-400 text-sm mb-1">Priority</p>
            <div className={`px-3 py-1 rounded-full text-sm inline-block ${getPriorityColor(task.priority)}`}>
              {task.priority}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <p className="text-gray-400 text-sm mb-1">Start Date</p>
            <p className="text-white">{formatDate(task.startDate)}</p>
          </div>
          <div>
            <p className="text-gray-400 text-sm mb-1">Due Date</p>
            <p className="text-white">{formatDate(task.dueDate)}</p>
          </div>
        </div>

        <div className="mb-4">
          <p className="text-gray-400 text-sm mb-1">Category</p>
          <p className="text-white">{task.category || 'Not categorized'}</p>
        </div>

        <div className="mb-4">
          <p className="text-gray-400 text-sm mb-1">Description</p>
          <div className="bg-gray-700/50 p-3 rounded text-white">
            {task.description || 'No description provided.'}
          </div>
        </div>

        <div className="mb-4">
          <p className="text-gray-400 text-sm mb-1">Assigned To</p>
          <div className="flex flex-wrap gap-2">
            {task.assignedTo && task.assignedTo.length > 0 ? (
              task.assignedTo.map((assignee, index) => (
                <span key={index} className="bg-purple-500/30 text-purple-200 px-2 py-1 rounded text-xs">
                  {assignee}
                </span>
              ))
            ) : (
              <span className="text-gray-400">Not assigned</span>
            )}
          </div>
        </div>

        {task.notes && (
          <div className="mb-4">
            <p className="text-gray-400 text-sm mb-1">Notes</p>
            <div className="bg-gray-700/50 p-3 rounded text-white">
              {task.notes}
            </div>
          </div>
        )}

        <div className="flex justify-end space-x-3">
          <button
            onClick={handleEdit}
            className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors flex items-center"
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit Task
          </button>
        </div>
      </div>
    </div>
  );
};

export default TaskDetailsModal;
