import { NextResponse } from "next/server"
import { pdfGeneratorTool } from "lib/tools/pdf-generator"
import { storageTool } from "lib/tools/storage-tool"

export async function GET(request) {
  try {
    // Get the PDF ID from the query parameters
    const { searchParams } = new URL(request.url)
    const id = searchParams.get("id")

    if (!id) {
      return NextResponse.json({ error: "Missing PDF ID" }, { status: 400 })
    }

    // Try to retrieve the PDF from storage first
    try {
      // This would be implemented in a real application
      // For now, we'll fall back to creating a mock PDF
    } catch (storageError) {
      console.log('Storage retrieval failed, creating mock PDF')
    }

    // Create a mock PDF using our PDF generator tool
    const pdfContents = [{
      title: 'Sample Content',
      content: 'This is a sample PDF generated by the Web Scraper application.\n\nIn a real application, this would contain the extracted and processed content from the selected URLs.'
    }]

    const pdfBuffer = await pdfGeneratorTool.generatePdf(pdfContents, {
      title: 'Web Content Extraction',
      subtitle: `Document ID: ${id}`,
      date: new Date().toLocaleDateString()
    })

    // Return the PDF
    return new NextResponse(pdfBuffer, {
      headers: {
        "Content-Type": "application/pdf",
        "Content-Disposition": `attachment; filename="web-content-${id}.pdf"`,
      },
    })
  } catch (error) {
    console.error("Error downloading PDF:", error)
    return NextResponse.json({ error: "Failed to download PDF" }, { status: 500 })
  }
}
