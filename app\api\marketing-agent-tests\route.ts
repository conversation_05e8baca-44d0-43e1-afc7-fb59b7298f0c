import { NextRequest, NextResponse } from 'next/server';
import { StrategicDirectorAgent } from '../../../lib/agents/marketing/StrategicDirectorAgent';
import { ResearchInsightsAgent } from '../../../lib/agents/marketing/ResearchInsightsAgent';
import { ContentCreatorAgent } from '../../../lib/agents/marketing/ContentCreatorAgent';
import { SocialMediaOrchestratorAgent } from '../../../lib/agents/marketing/SocialMediaOrchestratorAgent';
import { AnalyticsReportingAgent } from '../../../lib/agents/marketing/AnalyticsReportingAgent';
import { MarketingAgentManager } from '../../../lib/agents/marketing/MarketingAgentManager';
import { v4 as uuidv4 } from 'uuid';
import { adminDb } from '../../../components/firebase/admin';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/authOptions';

import { LlmProvider } from '../../../lib/tools/llm-tool';

// Define the request body interface
interface RequestBody {
  agentType: 'strategic-director' | 'research-insights' | 'content-creator' | 'social-media-orchestrator' | 'analytics-reporting' | 'team';
  prompt: string;
  includeThinking?: boolean;
  modelProvider?: string | LlmProvider; // Accept any string, we'll validate and convert it later
  modelName?: string;
  mode?: 'standard' | 'collaboration'; // Add mode parameter
  context?: string; // Custom context text
  documentReferences?: string[]; // Document references
  category?: string; // Document category
  userEmail?: string; // User email for authentication
  userId?: string; // User ID (email) for queryDocumentAgent and questionAnswerAgent
}

// Create a message collector for inter-agent communication
class MessageCollector {
  messages: { from: string; to: string; message: string }[] = [];

  collectMessage(from: string, to: string, message: string) {
    this.messages.push({ from, to, message });
    console.log(`Message from ${from} to ${to}: ${message}`);
  }

  getMessages() {
    return this.messages;
  }
}

export async function POST(req: NextRequest) {
  try {
    // Parse the request body
    const body: RequestBody = await req.json();
    let {
      agentType,
      prompt,
      includeThinking = false,
      modelProvider = 'openai' as LlmProvider,
      modelName = 'gpt-4o',
      mode = 'standard'
    } = body;

    // If mode is collaboration, redirect to the collaboration API
    if (mode === 'collaboration') {
      // Extract context options
      const { context, documentReferences, category } = body;

      // Prepare request body with context options
      const collaborationBody: any = {
        prompt,
        modelProvider,
        modelName,
        useModelForAllAgents: true,
        userId: body.userEmail || body.userId // Pass userId (email) for queryDocumentAgent and questionAnswerAgent
      };

      // Add context options if provided
      if (context) collaborationBody.context = context;
      if (documentReferences) collaborationBody.documentReferences = documentReferences;
      if (category) collaborationBody.category = category;

      // Generate a job ID upfront
      const jobId = uuidv4();

      // Create a job record immediately
      await adminDb.collection('marketingJobs').doc(jobId).set({
        status: 'processing',
        createdAt: new Date(),
        prompt: collaborationBody.prompt,
        userId: collaborationBody.userId || 'anonymous'
      });

      // Use a different approach - don't wait for the full response
      const fetchPromise = fetch(new URL('/api/marketing-agent-collaboration', req.url).toString(), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Job-Id': jobId
        },
        body: JSON.stringify(collaborationBody)
      }).then(async response => {
        try {
          if (!response.ok) {
            throw new Error(`Collaboration API error: ${response.status} ${response.statusText}`);
          }

          // Process the response in the background
          const responseData = await response.json();

          // Update the job with the result
          await adminDb.collection('marketingJobs').doc(jobId).update({
            status: 'completed',
            result: responseData,
            completedAt: new Date()
          });

          console.log(`Job ${jobId} completed successfully`);
        } catch (error) {
          console.error(`Error processing job ${jobId}:`, error);

          await adminDb.collection('marketingJobs').doc(jobId).update({
            status: 'failed',
            error: error instanceof Error ? error.message : String(error),
            errorAt: new Date()
          });
        }
      }).catch(error => {
        console.error(`Fetch error for job ${jobId}:`, error);

        adminDb.collection('marketingJobs').doc(jobId).update({
          status: 'failed',
          error: error instanceof Error ? error.message : String(error),
          errorAt: new Date()
        }).catch(console.error);
      });

      // Don't await the fetch - start it and return immediately
      fetchPromise.catch(console.error); // Handle any errors but don't wait

      // Return immediately with the job ID
      return NextResponse.json({
        success: true,
        isAsync: true,
        jobId,
        message: "Request is being processed asynchronously. Use the job ID to check status."
      }, { status: 202 });
    }

    // Validate model provider against LlmProvider type
    // LlmProvider = 'openai' | 'anthropic' | 'claude' | 'google' | 'groq'
    const validProviders: LlmProvider[] = ['openai', 'anthropic', 'claude', 'google', 'groq'];

    // Convert any non-standard provider to a valid one
    if (modelProvider === 'cohere') {
      console.warn(`Converting 'cohere' provider to 'openai' for compatibility`);
      modelProvider = 'openai' as LlmProvider;
    } else if (!validProviders.includes(modelProvider as any)) {
      console.warn(`Invalid model provider: ${modelProvider}. Falling back to openai.`);
      modelProvider = 'openai' as LlmProvider;
    } else {
      // Cast to LlmProvider type for TypeScript
      modelProvider = modelProvider as LlmProvider;
    }

    // Validate model name based on provider
    const validModels: Record<string, string[]> = {
      'openai': ['gpt-4o', 'gpt-4.1-2025-04-14', 'o3-2025-04-16', 'o3-mini-2025-01-31', 'o1-mini-2024-09-12'],
      'anthropic': ['claude-3-7-sonnet', 'claude-3-5-sonnet', 'claude-3-haiku'],
      'claude': ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
      'google': ['gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-2.0-flash', 'gemini-2.5-pro-preview-05-06'],
      'groq': ['deepseek-r1-distill-llama-70b', 'llama-3.3-70b-versatile']
    };

    // Check if the provider has models defined
    // Use a safer approach to check if the provider exists in validModels
    if (!(modelProvider in validModels)) {
      console.warn(`No models defined for provider: ${modelProvider}. Falling back to openai.`);
      modelProvider = 'openai' as LlmProvider;
      modelName = validModels['openai'][0];
    }
    // Check if the model is valid for the provider
    else if (!validModels[modelProvider as string].includes(modelName)) {
      console.warn(`Invalid model name: ${modelName} for provider: ${modelProvider}. Using default model.`);
      modelName = validModels[modelProvider as string][0];
    }

    console.log(`Using model provider: ${modelProvider}, model: ${modelName}`);
    console.log(`Processing request for agent type: ${agentType}`);
    console.log(`Request prompt: ${prompt.substring(0, 100)}${prompt.length > 100 ? '...' : ''}`);
    console.log(`Include thinking: ${includeThinking}`);

    // Create a message collector
    const messageCollector = new MessageCollector();

    // Create the appropriate agent based on the agent type
    let result: { thinking?: string; output: string; documentUrl?: string };

    // Get the user's session for authentication
    const session = await getServerSession(authOptions);

    // Determine if we're in a development/testing environment
    const isDevelopment = process.env.NODE_ENV === 'development';

    let userId: string;

    if (session?.user?.email) {
      // Use the authenticated user's email if available
      userId = session.user.email;
      console.log(`Using authenticated user ID: ${userId}`);
    } else if (isDevelopment) {
      // In development, allow a fallback user ID with a clear warning
      userId = 'test-user-' + Date.now();
      console.warn(`⚠️ WARNING: Using fallback test user ID (${userId}) because no authenticated user was found.`);
      console.warn(`⚠️ This is only allowed in development/testing environments.`);
      console.warn(`⚠️ To authenticate properly, visit /marketing-agent-tests/login`);
    } else {
      // In production, still require authentication
      console.error('Authentication required: No user session found in production environment');
      return NextResponse.json({ error: 'Unauthorized. User must be authenticated.' }, { status: 401 });
    }

    if (agentType === 'team') {
      // For team tests, use the MarketingAgentManager with the authenticated user
      console.log(`Creating MarketingAgentManager with authenticated userId: ${userId}`);

      const manager = new MarketingAgentManager({
        userId,
        defaultLlmProvider: modelProvider as LlmProvider,
        defaultLlmModel: modelName
      });

      // Override the message sending function to collect messages
      const originalSendMessage = manager.sendMessage;
      manager.sendMessage = function(from, to, message) {
        messageCollector.collectMessage(from, to, message);
        return originalSendMessage.call(this, from, to, message);
      };

      // Initialize the team
      let team;
      try {
        team = await manager.initializeMarketingTeam();
        console.log('Marketing team initialized successfully');
      } catch (error: unknown) {
        console.error('Error initializing marketing team:', error);
        const errorMessage = error instanceof Error ? error.message : String(error);
        throw new Error(`Failed to initialize marketing team: ${errorMessage}`);
      }

      // Process the request
      let thinking = '';

      // Make sure team is properly initialized
      if (!team) {
        throw new Error('Marketing team was not properly initialized');
      }

      if (includeThinking) {
        thinking = `# Marketing Team Thinking Process\n\n`;
        // Use type assertion to access the getThinking method
        thinking += `## Strategic Director\n${await (team.strategicDirector as any).getThinking(prompt)}\n\n`;
        thinking += `## Research Insights\n${await (team.researchInsights as any).getThinking(prompt)}\n\n`;
        thinking += `## Content Creator\n${await (team.contentCreator as any).getThinking(prompt)}\n\n`;
      }

      // Extract product name and description from the prompt
      const productInfo = extractProductInfo(prompt);

      // Create marketing campaign
      // Validate team members before using them
      if (!team.strategicDirector || !team.researchInsights || !team.contentCreator) {
        throw new Error('One or more required team members are missing');
      }

      // Use the team's strategic director to create a marketing strategy
      // Note: generateMarketingStrategy has been commented out to reduce complexity
      // Using a simpler approach instead
      const strategy = await team.strategicDirector.generateLongFormDocument(
        `Marketing strategy for ${productInfo.name}: ${productInfo.description}`,
        'markdown',
        'Focus on increasing brand awareness, generating leads, and driving sales'
      );

      // Get insights from research agent
      const insights = await team.researchInsights.conductMarketResearch(
        productInfo.name,
        'Professionals, Tech-savvy users, Health-conscious individuals',
        productInfo.description
      );

      // Generate content ideas - use type assertion for TypeScript
      const contentIdeas = await (team.contentCreator as any).generateContentIdeas(
        productInfo.name,
        productInfo.description,
        'Professionals, Tech-savvy users, Health-conscious individuals'
      );

      // Combine all outputs
      const output = `# Marketing Campaign for ${productInfo.name}

` +
        `## Strategy
${strategy}

` +
        `## Market Insights
${insights}

` +
        `## Content Ideas
${contentIdeas}

`;

      result = {
        thinking,
        output
      };
    } else {
      // For individual agent tests, use the authenticated user
      console.log(`Creating individual agent with authenticated userId: ${userId}`);

      let agent;

      try {
        switch (agentType) {
          case 'strategic-director':
            agent = new StrategicDirectorAgent('strategic-director', 'Strategic Director', userId, modelProvider as LlmProvider, modelName);
            break;
          case 'research-insights':
            agent = new ResearchInsightsAgent('research-insights', 'Research Insights', userId, modelProvider as LlmProvider, modelName);
            break;
          case 'content-creator':
            agent = new ContentCreatorAgent('content-creator', 'Content Creator', userId, modelProvider as LlmProvider, modelName);
            break;
          case 'social-media-orchestrator':
            agent = new SocialMediaOrchestratorAgent('social-media-orchestrator', 'Social Media Orchestrator', userId, modelProvider as LlmProvider, modelName);
            break;
          case 'analytics-reporting':
            agent = new AnalyticsReportingAgent('analytics-reporting', 'Analytics Reporting', userId, modelProvider as LlmProvider, modelName);
            break;
          default:
            throw new Error(`Invalid agent type: ${agentType}`);
        }
      } catch (error: unknown) {
        console.error(`Error creating agent of type ${agentType}:`, error);
        const errorMessage = error instanceof Error ? error.message : String(error);
        throw new Error(`Failed to create agent of type ${agentType}: ${errorMessage}`);
      }

      // Verify agent was created successfully
      if (!agent) {
        throw new Error(`Agent of type ${agentType} was not properly initialized`);
      }

      // Initialize variables
      let thinking = '';
      let output = '';
      let documentUrl = null;

      // For Strategic Director, check for document availability first
      if (agentType === 'strategic-director') {
        // First, query documents to check if we have enough information
        const documentQuery = `Find information related to: ${prompt}`;
        const queryResult = await (agent as StrategicDirectorAgent).queryDocuments(
          documentQuery,
          'marketing',
          undefined,
          "true" // Use internet search as fallback
        );

        // Check if the query result contains a request for information
        if (!queryResult.success && queryResult.metadata && 'requestForInformation' in queryResult.metadata) {
          console.log('Strategic Director: No documents found, stopping processing and requesting more information');

          // Return the request for information directly
          output = `I need more information to develop a comprehensive content strategy. ${queryResult.content}`;

          // Add thinking process if requested
          if (includeThinking) {
            thinking = "CONTEXT & CURRENT STATE RECOGNITION: • Search returned 'no relevant documents.' • Critical input gaps: industry, target audience, objectives, existing assets, channels, tone, KPIs, budget, competitors. • Therefore, step one is a discovery sprint to surface the missing data before drafting the final deliverable.";
          }
        } else {
          // If we have documents or the query was successful, proceed with normal processing
          // Get thinking process if requested
          if (includeThinking) {
            thinking = await (agent as any).getThinking(prompt);
          }

          // Process the request
          output = await (agent as any).processRequest(prompt);

          // Add a note about PDF generation
          output += '\n\n---\n\n**Note:** A PDF document with detailed strategic analysis and reasoning has been automatically generated and stored. The Strategic Director always documents analysis and reasoning for all requests.\n\n';

          // Get the latest document URL using the agent's method
          const latestDocUrl = (agent as StrategicDirectorAgent).getLatestDocumentUrl();
          if (latestDocUrl) {
            documentUrl = latestDocUrl;
            output += `[Download Strategic Analysis PDF](${documentUrl})\n\n`;
          }
        }
      }
      // Handle special cases for document generation for other agents
      else if (prompt.toLowerCase().includes('document') || prompt.toLowerCase().includes('pdf')) {
        try {
          if (prompt.toLowerCase().includes('strategy document')) {
            // Extract a strategy name from the prompt or use a default
            const strategyMatch = prompt.match(/for\s+["']([^"']+)["']/i);
            const strategyName = strategyMatch ? strategyMatch[1] : 'Marketing Strategy';

            // Generate a strategy document
            const docResult = await (agent as any).generateStrategyDocument(strategyName);
            output = `# Strategy Document Generated\n\nA PDF document has been generated for "${strategyName}".\n\n`;

            // Add document URL if available - using type assertion for TypeScript
            const typedResult = docResult as any;
            if (typedResult && typedResult.url) {
              documentUrl = typedResult.url;
              output += `[Download PDF Document](${documentUrl})\n\n`;
            } else {
              output += 'The document was generated but no download URL is available.\n\n';
            }

            // Add the document content as markdown
            if (typedResult && typedResult.content) {
              output += `## Document Content Preview:\n\n${typedResult.content}\n\n`;
            }
          } else {
            // Process as a regular request - use type assertion for TypeScript
            output = await (agent as any).processRequest(prompt);
          }
        } catch (error: unknown) {
          console.error('Error generating document:', error);
          const errorMessage = error instanceof Error ? error.message : String(error);
          output = await (agent as any).processRequest(prompt) + `\n\n*Note: Document generation was attempted but failed: ${errorMessage}*`;
        }
      } else {
        // For other agents, get thinking process if requested
        if (includeThinking) {
          thinking = await (agent as any).getThinking(prompt);
        }

        // Regular request processing for other agents - use type assertion for TypeScript
        output = await (agent as any).processRequest(prompt);
      }

      result = {
        thinking,
        output,
        documentUrl
      };
    }

    // Ensure messageCollector is properly initialized
    const agentMessages = messageCollector ? messageCollector.getMessages() : [];

    // Generate a unique request ID
    const requestId = uuidv4();

    // Prepare data to be stored in Firestore
    const agentOutputData = {
      requestId,
      timestamp: new Date(),
      agentType,
      userId, // Add userId field for proper filtering in PMO Output tab
      prompt,
      result,
      agentMessages,
      modelInfo: {
        provider: modelProvider,
        model: modelName
      }
    };

    // Store the data in Firestore
    try {
      console.log(`[AGENT_OUTPUT] Storing agent output with requestId: ${requestId}`);
      await adminDb.collection('Agent_Output').doc(requestId).set(agentOutputData);
      console.log(`[AGENT_OUTPUT] Successfully stored agent output with requestId: ${requestId}`);
    } catch (error) {
      console.error(`[AGENT_OUTPUT] Error storing agent output:`, error);
      // Continue with the response even if storage fails
    }

    // Return the result
    return NextResponse.json({
      ...result,
      requestId,
      agentMessages,
      modelInfo: {
        provider: modelProvider,
        model: modelName
      }
    });
  } catch (error) {
    console.error('Error processing marketing agent test request:', error);

    // Provide more detailed error information
    let errorMessage = 'Failed to process request';
    let errorDetails = error instanceof Error ? error.message : String(error);
    let statusCode = 500;

    // Check for specific error types
    if (errorDetails.includes('defaultLlmProvider')) {
      errorMessage = 'LLM Provider configuration error';
      errorDetails = 'There was an issue with the LLM provider configuration. Please check the provider and model settings.';
    }
    // Check for timeout or network errors
    else if (
      error instanceof Error &&
      (error.name === 'AbortError' ||
       errorDetails.includes('timeout') ||
       errorDetails.includes('network') ||
       errorDetails.includes('fetch failed'))
    ) {
      errorMessage = 'Request timeout';
      errorDetails = 'The request took too long to complete. This could be due to high server load or complex processing requirements. Please try again with a simpler query or try later.';
      statusCode = 504; // Gateway Timeout
    }
    // Check for Headers Timeout Error specifically
    else if (errorDetails.includes('HeadersTimeoutError') || errorDetails.includes('UND_ERR_HEADERS_TIMEOUT')) {
      errorMessage = 'Response headers timeout';
      errorDetails = 'The server took too long to respond with headers. This is often due to high server load or a complex request. Please try again with a simpler query or try later.';
      statusCode = 504; // Gateway Timeout
    }

    // Create a user-friendly response
    const response = {
      error: errorMessage,
      details: errorDetails,
      success: false,
      // Only include stack trace in development
      stack: process.env.NODE_ENV === 'development' && error instanceof Error ? error.stack : undefined
    };

    // Log the response we're sending back
    console.log(`Returning error response with status ${statusCode}:`, response);

    return NextResponse.json(response, { status: statusCode });
  }
}

/**
 * Helper function to extract product name and description from a prompt
 */
function extractProductInfo(prompt: string): { name: string; description: string } {
  // Default values
  let name = 'Product';
  let description = prompt;

  // Try to extract product name from quotes
  const nameMatch = prompt.match(/"([^"]+)"/);
  if (nameMatch && nameMatch[1]) {
    name = nameMatch[1];
  }

  // Try to extract description after a dash or colon
  const descMatch = prompt.match(/["-]\s*([^"]+)$/);
  if (descMatch && descMatch[1]) {
    description = descMatch[1].trim();
  }

  return { name, description };
}





