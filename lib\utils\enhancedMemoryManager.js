/**
 * enhancedMemoryManager.js
 * 
 * Enhanced utilities for managing memory in Node.js applications,
 * particularly for large document processing tasks.
 */

/**
 * Get detailed memory usage statistics
 * @param {string} label - Optional label for the log message
 * @returns {object} Memory usage statistics
 */
function getDetailedMemoryUsage(label = '') {
  const memoryUsage = process.memoryUsage();
  const heapUsed = Math.round(memoryUsage.heapUsed / (1024 * 1024));
  const heapTotal = Math.round(memoryUsage.heapTotal / (1024 * 1024));
  const rss = Math.round(memoryUsage.rss / (1024 * 1024));
  const external = Math.round(memoryUsage.external / (1024 * 1024));
  const arrayBuffers = Math.round(memoryUsage.arrayBuffers / (1024 * 1024));
  
  const percentHeapUsed = Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100);
  
  if (label) {
    console.log(`[${label}] Memory Usage:`);
    console.log(`  Heap Used: ${heapUsed} MB (${percentHeapUsed}% of total heap)`);
    console.log(`  Heap Total: ${heapTotal} MB`);
    console.log(`  RSS: ${rss} MB`);
    console.log(`  External: ${external} MB`);
    console.log(`  Array Buffers: ${arrayBuffers} MB`);
  }
  
  return {
    heapUsed,
    heapTotal,
    rss,
    external,
    arrayBuffers,
    percentHeapUsed,
    raw: memoryUsage
  };
}

/**
 * Aggressively tries to free memory
 * @returns {boolean} True if garbage collection was triggered
 */
function forceMemoryCleanup() {
  // First, clear any module caches that might be holding references
  Object.keys(require.cache).forEach(key => {
    if (!key.includes('node_modules')) {
      delete require.cache[key];
    }
  });
  
  // Clear any interval timers
  const timers = setTimeout(() => {}, 0);
  for (let i = 1; i < timers; i++) {
    clearTimeout(i);
  }
  
  // Try to run garbage collection multiple times
  let gcSuccess = false;
  try {
    if (typeof global.gc === 'function') {
      // Run GC multiple times to ensure thorough cleanup
      global.gc();
      
      // Small delay to allow GC to complete
      setTimeout(() => {
        global.gc();
      }, 100);
      
      gcSuccess = true;
    }
  } catch (error) {
    console.warn('Failed to trigger garbage collection:', error);
    console.warn('Make sure Node.js is started with --expose-gc flag');
  }
  
  return gcSuccess;
}

/**
 * Process large text in smaller chunks to avoid memory issues
 * @param {string} text - The large text to process
 * @param {function} processFn - Function to process each chunk
 * @param {number} chunkSize - Size of each chunk in characters
 * @returns {Promise<Array>} Array of results from processing each chunk
 */
async function processTextInChunks(text, processFn, chunkSize = 5000) {
  const results = [];
  const totalChunks = Math.ceil(text.length / chunkSize);
  
  console.log(`Processing text of length ${text.length} in ${totalChunks} chunks`);
  
  for (let i = 0; i < totalChunks; i++) {
    const start = i * chunkSize;
    const end = Math.min(start + chunkSize, text.length);
    const chunk = text.substring(start, end);
    
    console.log(`Processing chunk ${i+1}/${totalChunks} (${chunk.length} chars)`);
    
    try {
      // Process the current chunk
      const result = await processFn(chunk, i);
      results.push(result);
      
      // Clean up after each chunk
      if (i < totalChunks - 1) {
        getDetailedMemoryUsage(`After chunk ${i+1}/${totalChunks}`);
        forceMemoryCleanup();
        
        // Small delay to allow GC to complete
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    } catch (error) {
      console.error(`Error processing chunk ${i+1}/${totalChunks}:`, error);
      results.push(null); // Push null for failed chunks
    }
  }
  
  return results;
}

/**
 * Stream process a large file instead of loading it all into memory
 * @param {string} filePath - Path to the file
 * @param {function} processFn - Function to process each chunk
 * @param {number} chunkSize - Size of each chunk in bytes
 * @returns {Promise<Array>} Array of results from processing each chunk
 */
async function streamProcessFile(filePath, processFn, chunkSize = 65536) {
  const fs = require('fs');
  const { createReadStream } = fs;
  const { promisify } = require('util');
  const { createInterface } = require('readline');
  
  return new Promise((resolve, reject) => {
    const results = [];
    let chunkIndex = 0;
    let buffer = '';
    
    const readStream = createReadStream(filePath, {
      highWaterMark: chunkSize,
      encoding: 'utf8'
    });
    
    readStream.on('data', async (chunk) => {
      // Pause the stream to process the current chunk
      readStream.pause();
      
      buffer += chunk;
      
      // Process complete chunks
      if (buffer.length >= chunkSize) {
        const processChunk = buffer.substring(0, chunkSize);
        buffer = buffer.substring(chunkSize);
        
        try {
          console.log(`Processing file chunk ${++chunkIndex}`);
          const result = await processFn(processChunk, chunkIndex);
          results.push(result);
          
          // Clean up after processing
          getDetailedMemoryUsage(`After file chunk ${chunkIndex}`);
          forceMemoryCleanup();
          
          // Small delay to allow GC to complete
          await new Promise(r => setTimeout(r, 200));
        } catch (error) {
          console.error(`Error processing file chunk ${chunkIndex}:`, error);
          results.push(null);
        }
      }
      
      // Resume the stream
      readStream.resume();
    });
    
    readStream.on('end', async () => {
      // Process any remaining data
      if (buffer.length > 0) {
        try {
          console.log(`Processing final file chunk ${++chunkIndex}`);
          const result = await processFn(buffer, chunkIndex);
          results.push(result);
        } catch (error) {
          console.error(`Error processing final file chunk:`, error);
          results.push(null);
        }
      }
      
      resolve(results);
    });
    
    readStream.on('error', (error) => {
      reject(error);
    });
  });
}

module.exports = {
  getDetailedMemoryUsage,
  forceMemoryCleanup,
  processTextInChunks,
  streamProcessFile
};