// research/DataAnalystSynthesizerAgent.ts

import { ResearchAgent } from './ResearchAgent';
import { Llm<PERSON>rovider } from '../../tools/llm-tool';
import { RetrievalResult, AnalyzedSource, SynthesizedFindings } from './ResearchInterfaces';

export class DataAnalystSynthesizerAgent extends ResearchAgent {
    constructor(
        id: string = 'data-analyst',
        name: string = 'Data Analyst & Synthesizer',
        userId: string = '',
        defaultLlmProvider: LlmProvider = 'openai',
        defaultLlmModel: string = 'gpt-4o'
    ) {
        const role = 'Data Analysis and Synthesis Specialist';
        const description = `I analyze raw information provided by retrievers, evaluate source credibility, extract key findings, identify themes and patterns, and synthesize this into a coherent understanding.`;
        super(id, name, role, description, userId, defaultLlmProvider, defaultLlmModel);
    }

    /**
     * Analyze retrieved information and synthesize findings
     * @param results - Array of retrieval results
     * @param subTaskId - The specific sub-task this analysis pertains to
     * @param parentTaskId - The overall research task ID
     * @param researchQuestion - The guiding question for this analysis
     * @returns Synthesized findings
     */
    async analyzeAndSynthesize(
        results: RetrievalResult[],
        subTaskId: string,
        parentTaskId: string,
        researchQuestion: string
    ): Promise<SynthesizedFindings> {
        console.log(`[${this.name}] Analyzing ${results.length} results for sub-task ${subTaskId}: ${researchQuestion}`);

        if (results.length === 0) {
            console.warn(`[${this.name}] No results provided for analysis.`);
            return {
                synthesisId: `syn-${subTaskId}-${Date.now()}`,
                subTaskId,
                parentTaskId,
                summary: "No information retrieved for this topic.",
                keyThemes: [],
                supportingEvidence: {},
                analyzedSources: [],
                confidenceLevel: 'low',
            };
        }

        // Prepare context for LLM analysis
        const sourcesText = results.map((r, index) =>
            `Source ${index + 1} (URL: ${r.sourceUrl}, Reliability: ${r.initialReliabilityScore?.toFixed(2) ?? 'N/A'}):\n` +
            `Title: ${r.title}\n` +
            `Content Snippet:\n${r.formattedContent || r.rawContent.substring(0, 1500)}...\n---`
        ).join('\n\n');

        const prompt = `
          Research Question: ${researchQuestion}

          Analyze the following ${results.length} sources to answer the research question.
          For each source, assess its relevance and credibility more deeply based on the content provided.
          Extract key findings relevant to the question.
          Identify the main themes emerging across the sources.
          Note any significant agreements, contradictions, or gaps.
          Provide a concise synthesized summary answering the research question.
          Finally, assess the overall confidence level (low, medium, high) in the synthesized findings based on the source quality and consistency.

          Sources:
          ${sourcesText}

          Format your response as a JSON object with keys:
          - "analyzedSources": An array of objects, each with "sourceUrl", "keyFindings" (array of strings), "relevanceScore" (0-1), "credibilityScore" (0-1), "biasAssessment" (string).
          - "keyThemes": An array of strings.
          - "summary": A synthesized paragraph answering the research question.
          - "contradictions": (Optional) An array of strings describing contradictions.
          - "gaps": (Optional) An array of strings describing information gaps.
          - "confidenceLevel": 'low', 'medium', or 'high'.
        `;

        const responseJson = await this.processRequest(prompt);
        let synthesis: SynthesizedFindings;

        try {
            const parsed = JSON.parse(responseJson);

            // Map parsed sources to AnalyzedSource structure
             const analyzedSources: AnalyzedSource[] = (parsed.analyzedSources || []).map((ps: any, index: number) => ({
                analysisId: `ana-${results[index]?.resultId || index}-${Date.now()}`,
                retrievalResultId: results[index]?.resultId || `unknown-${index}`,
                subTaskId: subTaskId,
                sourceUrl: ps.sourceUrl || results[index]?.sourceUrl || 'Unknown',
                keyFindings: ps.keyFindings || [],
                relevanceScore: ps.relevanceScore || 0.5,
                credibilityScore: ps.credibilityScore || 0.5,
                biasAssessment: ps.biasAssessment || 'Not assessed',
            }));


            synthesis = {
                synthesisId: `syn-${subTaskId}-${Date.now()}`,
                subTaskId,
                parentTaskId,
                summary: parsed.summary || "Analysis failed to produce a summary.",
                keyThemes: parsed.keyThemes || [],
                supportingEvidence: {}, // Needs population based on themes and sources - complex mapping
                contradictions: parsed.contradictions || [],
                gaps: parsed.gaps || [],
                confidenceLevel: parsed.confidenceLevel || 'medium',
                analyzedSources: analyzedSources,
            };
             console.log(`[${this.name}] Analysis complete for ${subTaskId}. Confidence: ${synthesis.confidenceLevel}`);

        } catch (error) {
            console.error(`[${this.name}] Failed to parse analysis response for ${subTaskId}:`, error);
            synthesis = { // Provide fallback structure
                synthesisId: `syn-${subTaskId}-${Date.now()}-error`,
                subTaskId, parentTaskId,
                summary: "Error during analysis.", keyThemes: [], supportingEvidence: {}, analyzedSources: [], confidenceLevel: 'low',
            };
        }

        return synthesis;
    }

     /**
      * Handles incoming tasks/messages for analysis and synthesis
      */
     async handleTask(messageContent: string, metadata: Record<string, any>): Promise<void> {
         // Expecting retrieval results to be passed in metadata
         if (metadata.subTaskId && metadata.instruction && metadata.retrievalResults) {
             console.log(`[${this.name}] Received task: ${metadata.subTaskId} - ${metadata.instruction}`);
             const results: RetrievalResult[] = metadata.retrievalResults;
             const parentTaskId = metadata.parentTaskId || 'unknown_parent';
             const researchQuestion = metadata.instruction.replace(/^Analyze retrieved information for /i, '').trim(); // Basic parsing

             const synthesis = await this.analyzeAndSynthesize(results, metadata.subTaskId, parentTaskId, researchQuestion);

             // Send results back to the Lead or directly to Writer?
             const leadAgentId = 'research-lead'; // Placeholder
             await this.sendMessage(
                 leadAgentId,
                 `Analysis complete for sub-task ${metadata.subTaskId}`,
                 {
                     type: 'analysis_complete',
                     subTaskId: metadata.subTaskId,
                     synthesis: synthesis // Attach results
                 }
             );
             await this.updateTaskStatus(metadata.taskId || metadata.subTaskId, 'completed');

         } else {
             console.warn(`[${this.name}] Received message missing required data for analysis:`, metadata);
         }
     }
}