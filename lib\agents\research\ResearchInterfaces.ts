// research/ResearchInterfaces.ts

export interface ResearchTaskRequest {
    taskId: string; // Link back to the overall task
    topic: string;
    scope?: string; // Specific questions, areas to focus on/exclude
    requiredDepth?: 'surface' | 'moderate' | 'deep';
    outputFormat?: 'summary' | 'report' | 'presentation_points' | 'raw_data';
    deadline?: Date;
    requesterInfo?: string; // Who asked for it
}

export interface ResearchTaskBrief extends ResearchTaskRequest {
    clarifiedScope: string;
    keyQuestions: string[];
    potentialSources?: string[]; // URLs, databases, keywords
    constraints?: string[]; // e.g., date ranges, source types
}

export interface SubTask {
    subTaskId: string;
    parentTaskId: string;
    assignedToAgentId: string;
    instruction: string; // Specific action for the agent
    status: 'pending' | 'in-progress' | 'completed' | 'failed';
    result?: any; // To store the outcome briefly
    requiredInputs?: string[]; // Dependencies, e.g., output from another sub-task
}

export interface ResearchPlan {
    planId: string;
    parentTaskId: string;
    brief: ResearchTaskBrief;
    subTasks: SubTask[];
    overallStatus: 'pending' | 'in-progress' | 'completed' | 'failed';
}

export interface SearchQuery {
    queryId: string;
    subTaskId: string;
    queryText: string; // Search terms or specific URL
    sourceType: 'web' | 'academic' | 'database' | 'news'; // Guides the retriever
    constraints?: Record<string, any>; // e.g., date_after, site_filter
}

export interface RetrievalResult {
    resultId: string;
    queryId: string;
    subTaskId: string;
    sourceUrl: string;
    title: string;
    rawContent: string; // Snippet or full text if reasonable
    formattedContent?: string; // Cleaned/Markdown version
    metadata?: Record<string, string | null>;
    retrievedAt: Date;
    initialReliabilityScore?: number; // 0-1, basic assessment
}

export interface AnalyzedSource {
    analysisId: string;
    retrievalResultId: string;
    subTaskId: string;
    sourceUrl: string;
    keyFindings: string[];
    quotes?: string[];
    relevanceScore: number; // 0-1 to the sub-task
    credibilityScore: number; // 0-1 based on deeper analysis
    biasAssessment?: string;
    notes?: string;
}

export interface SynthesizedFindings {
    synthesisId: string;
    subTaskId: string; // Or could be linked to multiple sub-tasks
    parentTaskId: string;
    summary: string;
    keyThemes: string[];
    supportingEvidence: Record<string, string[]>; // Map theme/point to source URLs/analysisIds
    contradictions?: string[];
    gaps?: string[];
    confidenceLevel?: 'low' | 'medium' | 'high';
    analyzedSources: AnalyzedSource[];
}

export interface FormattingGuidelines {
    format: 'summary' | 'report' | 'presentation_points' | 'raw_data';
    styleGuide?: 'APA' | 'MLA' | 'Chicago' | 'Simple';
    length?: string; // e.g., "approx 5 pages", "10 slides"
    sections?: string[]; // Required sections for a report
}

export interface ReportDraft {
    draftId: string;
    synthesisId: string;
    parentTaskId: string;
    title: string;
    content: string; // Formatted content (e.g., Markdown)
    bibliography?: string[];
    version: number;
}

export interface QaChecklistItem {
    check: string;
    status: 'pass' | 'fail' | 'n/a';
    comments?: string;
}

export interface ReviewFeedback {
    reviewId: string;
    draftId: string;
    parentTaskId: string;
    overallStatus: 'approved' | 'rejected' | 'needs_revision';
    checklist: QaChecklistItem[];
    summaryFeedback?: string;
    reviewerAgentId: string;
}

export interface FinalResearchReport {
    reportId: string;
    parentTaskId: string;
    brief: ResearchTaskBrief;
    reportTitle: string;
    reportContent: string; // Final, potentially PDF content or structured data
    generatedPdf?: Buffer; // Optional generated PDF
    deliveryTimestamp: Date;
}

// PMO Integration Extensions
export interface PMOResearchTaskRequest extends ResearchTaskRequest {
    pmoId?: string;
    pmoAssessment?: string;
    teamSelectionRationale?: string;
    priority?: string;
    category?: string;
    requirementsDocument?: string;
    crossTeamCoordination?: boolean;
}

export interface EnhancedResearchPlan extends ResearchPlan {
    pmoContext?: {
        pmoId: string;
        projectTitle: string;
        projectDescription: string;
        priority: string;
        category: string;
    };
    strategicTaskCollectionId?: string;
    crossTeamTasks?: SubTask[];
    teamAssignments?: {
        [teamName: string]: {
            taskCount: number;
            taskIds: string[];
            estimatedWorkload: string;
        };
    };
}

export interface ResearchStrategicTask {
    id: string;
    title: string;
    description: string;
    category: 'Research' | 'Strategic Planning' | 'Implementation' | 'Market Intelligence' | 'Product Analysis' | 'Customer Intelligence' | 'Marketing Infrastructure' | 'Performance Metrics' | 'Customer Validation' | 'Content Creation';
    priority: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
    assignedTeam: 'Research Team' | 'Business Analysis Team' | 'Marketing Team' | 'Sales Team' | 'Software Design Team' | 'Content Team' | 'Strategic Director';
    status: 'IDENTIFIED' | 'ASSIGNED' | 'IN_PROGRESS' | 'REVIEW' | 'COMPLETED' | 'BLOCKED' | 'CANCELLED';
    specificRequirements: string[];
    deliverable: string;
    timeline: {
        estimatedDuration: string;
        startDate?: Date;
        dueDate?: Date;
        milestones?: Array<{
            name: string;
            date: Date;
            description: string;
            completed: boolean;
        }>;
    };
    dependencies?: string[];
    blockers?: string[];
    successCriteria: string[];
    resources?: {
        budget?: number;
        tools?: string[];
        personnel?: string[];
        documents?: string[];
    };
    metadata: {
        createdAt: Date;
        createdBy: string;
        updatedAt: Date;
        source: 'Information Gap Analysis' | 'Strategic Planning' | 'PMO Requirements' | 'User Request' | 'System Generated';
        pmoId?: string;
        projectId?: string;
        requestId?: string;
    };
}

export interface ResearchCapabilities {
    researchCapabilities: string[];
    pmoIntegration: string[];
    crossTeamCoordination: string[];
    strategicPlanning: string[];
}