'use client';

import React, { useState } from 'react';
import { X, Check, Trash2, Plus, ChevronDown, ChevronRight } from 'lucide-react';
import { Button } from '../ui/button';
import { Task, Subtask } from '../../admin/planner/types';

interface ProjectData {
  projectName: string;
  projectDescription: string;
  startDate: string;
  endDate: string;
  categories: string[];
  priority: 'High' | 'Medium' | 'Low';
  estimatedDuration: string;
}

interface TaskData {
  id: string;
  title: string;
  description: string;
  category: string;
  priority: 'High' | 'Medium' | 'Low';
  dueDate: string;
  assignedTo?: string[]; // Team assignments from Strategic Analysis
  teamAssignment?: string; // Original team name from Strategic Analysis (e.g., "Research Team", "Marketing Team")
  subtasks?: SubtaskData[];
  approved: boolean;
}

interface SubtaskData {
  id: string;
  title: string;
  description: string;
  priority: 'High' | 'Medium' | 'Low';
  dueDate?: string;
  approved: boolean;
}

interface ProjectCreationReviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: ProjectData;
  tasks: TaskData[];
  onCommit: (approvedTasks: TaskData[]) => Promise<void>;
  isCommitting?: boolean;
}

export default function ProjectCreationReviewModal({
  isOpen,
  onClose,
  project,
  tasks: initialTasks,
  onCommit,
  isCommitting = false
}: ProjectCreationReviewModalProps) {
  const [tasks, setTasks] = useState<TaskData[]>(initialTasks);
  const [expandedTasks, setExpandedTasks] = useState<Set<string>>(new Set());

  if (!isOpen) return null;

  const toggleTaskApproval = (taskId: string) => {
    setTasks(prev => prev.map(task =>
      task.id === taskId
        ? { ...task, approved: !task.approved }
        : task
    ));
  };

  const toggleSubtaskApproval = (taskId: string, subtaskId: string) => {
    setTasks(prev => prev.map(task =>
      task.id === taskId
        ? {
            ...task,
            subtasks: task.subtasks?.map(subtask =>
              subtask.id === subtaskId
                ? { ...subtask, approved: !subtask.approved }
                : subtask
            )
          }
        : task
    ));
  };

  const toggleTaskExpansion = (taskId: string) => {
    setExpandedTasks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(taskId)) {
        newSet.delete(taskId);
      } else {
        newSet.add(taskId);
      }
      return newSet;
    });
  };

  const removeTask = (taskId: string) => {
    setTasks(prev => prev.filter(task => task.id !== taskId));
  };

  const removeSubtask = (taskId: string, subtaskId: string) => {
    setTasks(prev => prev.map(task =>
      task.id === taskId
        ? {
            ...task,
            subtasks: task.subtasks?.filter(subtask => subtask.id !== subtaskId)
          }
        : task
    ));
  };

  const approvedTasks = tasks.filter(task => task.approved);
  const totalTasks = tasks.length;
  const totalSubtasks = tasks.reduce((sum, task) => sum + (task.subtasks?.length || 0), 0);
  const approvedSubtasks = tasks.reduce((sum, task) =>
    sum + (task.subtasks?.filter(subtask => subtask.approved).length || 0), 0
  );

  const handleCommit = async () => {
    const tasksToCommit = tasks.filter(task => task.approved).map(task => ({
      ...task,
      subtasks: task.subtasks?.filter(subtask => subtask.approved)
    }));
    await onCommit(tasksToCommit);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div>
            <h2 className="text-2xl font-bold text-white">Review Project Creation</h2>
            <p className="text-gray-400 mt-1">Review and approve tasks before creating the project</p>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {/* Project Info */}
          <div className="bg-gray-800 rounded-lg p-6 mb-6">
            <h3 className="text-xl font-semibold text-white mb-4">Project Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-300">Project Name</label>
                <p className="text-white mt-1">{project.projectName}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-300">Priority</label>
                <p className="text-white mt-1">{project.priority}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-300">Start Date</label>
                <p className="text-white mt-1">{project.startDate}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-300">End Date</label>
                <p className="text-white mt-1">{project.endDate}</p>
              </div>
              <div className="md:col-span-2">
                <label className="text-sm font-medium text-gray-300">Description</label>
                <p className="text-white mt-1">{project.projectDescription}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-300">Categories</label>
                <p className="text-white mt-1">{project.categories.join(', ')}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-300">Estimated Duration</label>
                <p className="text-white mt-1">{project.estimatedDuration}</p>
              </div>
            </div>
          </div>

          {/* Tasks Summary */}
          <div className="bg-gray-800 rounded-lg p-4 mb-6">
            <h3 className="text-lg font-semibold text-white mb-2">Tasks Summary</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <p className="text-2xl font-bold text-blue-400">{totalTasks}</p>
                <p className="text-sm text-gray-400">Total Tasks</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-green-400">{approvedTasks.length}</p>
                <p className="text-sm text-gray-400">Approved Tasks</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-purple-400">{totalSubtasks}</p>
                <p className="text-sm text-gray-400">Total Subtasks</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-green-400">{approvedSubtasks}</p>
                <p className="text-sm text-gray-400">Approved Subtasks</p>
              </div>
            </div>
          </div>

          {/* Tasks List */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">Tasks ({tasks.length})</h3>
            {tasks.map((task) => (
              <div key={task.id} className="bg-gray-800 rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleTaskApproval(task.id)}
                        className={`p-1 ${
                          task.approved
                            ? 'text-green-400 hover:text-green-300'
                            : 'text-gray-400 hover:text-white'
                        }`}
                      >
                        <Check className="h-4 w-4" />
                      </Button>
                      {task.subtasks && task.subtasks.length > 0 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleTaskExpansion(task.id)}
                          className="p-1 text-gray-400 hover:text-white"
                        >
                          {expandedTasks.has(task.id) ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                        </Button>
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className={`font-medium ${task.approved ? 'text-white' : 'text-gray-400'}`}>
                        {task.title}
                      </h4>
                      <p className="text-sm text-gray-400 mt-1">{task.description}</p>
                      <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                        <span>Priority: {task.priority}</span>
                        <span>Category: {task.category}</span>
                        <span>Due: {task.dueDate}</span>
                        {task.teamAssignment && (
                          <span className="bg-blue-500/20 text-blue-300 px-2 py-1 rounded">
                            Team: {task.teamAssignment}
                          </span>
                        )}
                        {task.subtasks && task.subtasks.length > 0 && (
                          <span>{task.subtasks.length} subtasks</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeTask(task.id)}
                    className="text-red-400 hover:text-red-300 p-1"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>

                {/* Subtasks */}
                {task.subtasks && task.subtasks.length > 0 && expandedTasks.has(task.id) && (
                  <div className="mt-4 ml-8 space-y-2">
                    {task.subtasks.map((subtask) => (
                      <div key={subtask.id} className="bg-gray-700 rounded p-3">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-3 flex-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => toggleSubtaskApproval(task.id, subtask.id)}
                              className={`p-1 ${
                                subtask.approved
                                  ? 'text-green-400 hover:text-green-300'
                                  : 'text-gray-400 hover:text-white'
                              }`}
                            >
                              <Check className="h-3 w-3" />
                            </Button>
                            <div className="flex-1">
                              <h5 className={`text-sm font-medium ${subtask.approved ? 'text-white' : 'text-gray-400'}`}>
                                {subtask.title}
                              </h5>
                              <p className="text-xs text-gray-400 mt-1">{subtask.description}</p>
                              <div className="flex items-center space-x-3 mt-1 text-xs text-gray-500">
                                <span>Priority: {subtask.priority}</span>
                                {subtask.dueDate && <span>Due: {subtask.dueDate}</span>}
                              </div>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeSubtask(task.id, subtask.id)}
                            className="text-red-400 hover:text-red-300 p-1"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-400">
              {approvedTasks.length} of {totalTasks} tasks approved
              {totalSubtasks > 0 && `, ${approvedSubtasks} of ${totalSubtasks} subtasks approved`}
            </div>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={onClose}
                disabled={isCommitting}
                className="border-gray-600 text-gray-300 hover:bg-gray-800"
              >
                Cancel
              </Button>
              <Button
                onClick={handleCommit}
                disabled={isCommitting || approvedTasks.length === 0}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                {isCommitting ? 'Creating...' : `Commit Project (${approvedTasks.length} tasks)`}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
