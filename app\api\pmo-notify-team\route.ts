import { NextRequest, NextResponse } from 'next/server';
import { addDoc, collection } from 'firebase/firestore';
import { db } from '../../../components/firebase';

/**
 * API endpoint to notify teams about PMO requirements
 * Enhanced to automatically trigger marketing collaboration for Marketing team
 */
export async function POST(request: NextRequest) {
  try {
    const {
      pmoId,
      teamId,
      teamName,
      projectTitle,
      projectDescription,
      pmoAssessment,
      teamSelectionRationale,
      priority,
      category,
      userId,
      pmoRecord,
      metadata
    } = await request.json();

    // Validate required fields
    if (!pmoId || !teamId || !teamName || !projectTitle || !userId) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create team notification record
    const teamNotification = {
      pmoId,
      teamId,
      teamName,
      projectTitle,
      projectDescription: projectDescription || '',
      pmoAssessment: pmoAssessment || '',
      teamSelectionRationale: teamSelectionRationale || '',
      priority: priority || 'Medium',
      category,
      userId,
      status: 'Pending Strategic Plan',
      notifiedAt: new Date(),
      requiresStrategicPlan: true,
      strategicPlanCreated: false,
      metadata: {
        ...metadata,
        documentType: 'Team Notification',
        source: 'PMO'
      }
    };

    // Save to team notifications collection
    const notificationRef = await addDoc(
      collection(db, 'users', userId, 'teamNotifications'),
      teamNotification
    );

    // Also create a record in the team-specific collection for easy access
    const teamSpecificNotification = {
      ...teamNotification,
      notificationId: notificationRef.id,
      teamSpecific: true
    };

    await addDoc(
      collection(db, 'users', userId, `${teamName.toLowerCase().replace(/\s+/g, '')}TeamTasks`),
      teamSpecificNotification
    );

    let marketingCollaborationResult = null;

    // If this is for the Marketing team, automatically trigger marketing collaboration
    if (teamName.toLowerCase() === 'marketing') {
      try {
        marketingCollaborationResult = await triggerMarketingCollaboration({
          pmoId,
          projectTitle,
          projectDescription,
          pmoAssessment,
          teamSelectionRationale,
          priority,
          category,
          userId,
          notificationId: notificationRef.id,
          pmoRecord
        });
      } catch (marketingError) {
        console.error('Error triggering marketing collaboration:', marketingError);
        // Don't fail the entire request if marketing collaboration fails
        // The notification was still created successfully
      }
    }

    return NextResponse.json({
      success: true,
      notificationId: notificationRef.id,
      message: `Requirements sent to ${teamName} team successfully`,
      marketingCollaboration: marketingCollaborationResult
    });

  } catch (error) {
    console.error('Error notifying team:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to notify team'
      },
      { status: 500 }
    );
  }
}

/**
 * Trigger marketing collaboration workflow from PMO notification
 */
async function triggerMarketingCollaboration(pmoData: {
  pmoId: string;
  projectTitle: string;
  projectDescription: string;
  pmoAssessment: string;
  teamSelectionRationale: string;
  priority: string;
  category: string;
  userId: string;
  notificationId: string;
  pmoRecord?: any;
}) {
  // Send the original user request description as the prompt for the marketing team
  // The PMO assessment and other details will be provided as context
  const marketingPrompt = pmoData.projectDescription;

  // Prepare comprehensive context from PMO assessment for the marketing team
  const contextInformation = `
# PMO Marketing Requirements Analysis Context

## Project Overview
**Title:** ${pmoData.projectTitle}
**Priority:** ${pmoData.priority}
**PMO ID:** ${pmoData.pmoId}

## PMO Assessment
${pmoData.pmoAssessment}

## Team Selection Rationale
${pmoData.teamSelectionRationale}

## Marketing Team Objectives
Based on the PMO requirements above, please provide a comprehensive marketing strategy analysis that includes:

1. **Strategic Marketing Assessment** - Analyze the marketing implications and opportunities
2. **Target Audience Analysis** - Identify and profile the target market segments
3. **Competitive Landscape** - Research and analyze competitive positioning
4. **Marketing Channel Strategy** - Recommend optimal marketing channels and tactics
5. **Content Strategy** - Develop content themes and messaging framework
6. **Campaign Planning** - Outline campaign structure and timeline
7. **Success Metrics** - Define KPIs and measurement framework
8. **Resource Requirements** - Estimate budget and resource needs

Please ensure your analysis is comprehensive and actionable, providing specific recommendations that the marketing team can implement.

## Additional Context
${pmoData.pmoRecord?.customContext ? `- Custom Context: ${pmoData.pmoRecord.customContext}` : ''}
${pmoData.pmoRecord?.contextCategories?.length ? `- Source Document Categories: ${pmoData.pmoRecord.contextCategories.join(', ')}` : ''}
  `.trim();

  // Use the first contextCategory as the primary category for document search
  const documentCategory = pmoData.pmoRecord?.contextCategories?.length > 0
    ? pmoData.pmoRecord.contextCategories[0]
    : pmoData.category;

  // Call the marketing-agent-collaboration API
  const collaborationBody = {
    prompt: marketingPrompt,
    modelProvider: 'openai',
    modelName: 'o3-2025-04-16',
    userId: pmoData.userId,
    context: contextInformation,
    category: documentCategory, // Use PMO's contextCategories for document search
    metadata: {
      source: 'PMO',
      pmoId: pmoData.pmoId,
      recordTitle: pmoData.projectTitle, // Add PMO title for project naming
      projectTitle: pmoData.projectTitle, // Also add as projectTitle for compatibility
      notificationId: pmoData.notificationId,
      autoTriggered: true,
      triggerTimestamp: new Date().toISOString(),
      pmoContextCategories: pmoData.pmoRecord?.contextCategories || [],
      pmoCustomContext: pmoData.pmoRecord?.customContext || null
    }
  };

  const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/marketing-agent-collaboration`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(collaborationBody)
  });

  if (!response.ok) {
    throw new Error(`Marketing collaboration API error: ${response.status} ${response.statusText}`);
  }

  const result = await response.json();

  return {
    success: true,
    requestId: result.requestId,
    message: 'Marketing collaboration triggered successfully',
    collaborationData: result
  };
}
