/**
 * Document Processing API
 *
 * This API provides endpoints for asynchronous document processing:
 * - POST /api/document-processing: Submit a document processing job
 * - GET /api/document-processing/:id: Get the status of a document processing job
 * - DELETE /api/document-processing/:id: Cancel a document processing job
 */

import { NextRequest, NextResponse } from 'next/server';
import { documentProcessingQueue, JobStatus, JobPriority, DocumentProcessingJob } from '../../../lib/queue/documentProcessingQueue'; // Adjust path as needed
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/authOptions'; // Adjust path as needed
import { StrategicDirectorAgent } from '../../../lib/agents/marketing/StrategicDirectorAgent'; // Adjust path as needed

// Define a type for the user/actor identifier obtained after authentication.
interface AuthenticatedActor {
  id: string; // Represents the user ID (e.g., email or 'anonymous' for session users, or provided ID for internal calls)
}

// Define a type for Session from next-auth for stronger typing
interface Session {
  user?: {
    email?: string;
    name?: string;
    image?: string;
  };
  expires?: string;
}

/**
 * Unified authentication helper: supports session-based auth and internal secret-based auth.
 * Returns the authenticated actor or an error response.
 */
async function authenticateAndGetActor(request: NextRequest): Promise<{ actor: AuthenticatedActor | null; response: NextResponse | null }> {
  const internalAuthHeader = request.headers.get('X-Internal-Auth');
  const internalSecretFromEnv = process.env.INTERNAL_API_SECRET;

  // Enable debugging to help troubleshoot authentication issues
  console.log(`[Auth Debug] Received X-Internal-Auth header: ${internalAuthHeader ? "Present" : "Missing"}`);
  console.log(`[Auth Debug] ENV INTERNAL_API_SECRET: ${internalSecretFromEnv ? "Configured" : "Missing"}`);

  // Handle internal authentication
  if (internalAuthHeader) {
    if (!internalSecretFromEnv || internalSecretFromEnv.trim() === '') {
      console.error('INTERNAL_API_SECRET environment variable is not configured properly for document processing API.');
      return {
        actor: null,
        response: NextResponse.json({ error: 'Internal authentication is not configured correctly on the server.' }, { status: 500 })
      };
    }

    // Trim both header and env var for comparison to avoid whitespace issues
    if (internalAuthHeader.trim() === internalSecretFromEnv.trim()) {
      const actingAsUserId = request.headers.get('X-Acting-As-User-Id');
      if (actingAsUserId && actingAsUserId.trim() !== '') {
        console.log(`Internal authentication successful. Acting as user: ${actingAsUserId.trim()}`);
        return { actor: { id: actingAsUserId.trim() }, response: null };
      } else {
        // If no user ID is provided, use a default system user ID
        console.log(`Internal authentication successful. No user ID provided, using system default.`);
        return { actor: { id: 'system-default-user' }, response: null };
      }
    } else {
      console.warn('Internal call attempt with invalid X-Internal-Auth secret.');
      console.warn(`[Auth Debug] Secret mismatch detected. Please check configuration.`);
      return { actor: null, response: NextResponse.json({ error: 'Invalid internal authentication credentials.' }, { status: 401 }) };
    }
  }

  // Standard session-based authentication (for external users / clients)
  try {
    console.log('Attempting session-based authentication for document processing API.');
    const session = await getServerSession(authOptions) as Session | null;

    if (!session?.user?.email) { // Prefer email as a more stable ID
      console.warn('No valid session or user email found for the request.');
      return { actor: null, response: NextResponse.json({ error: 'Unauthorized. Please log in.' }, { status: 401 }) };
    }

    const userIdFromSession = session.user.email;
    console.log(`User authenticated via session: ${userIdFromSession}`);
    return { actor: { id: userIdFromSession }, response: null };
  } catch (error) {
    console.error('Error during session authentication for document processing API:', error);
    return {
      actor: null,
      response: NextResponse.json({ error: 'Authentication error occurred.' }, { status: 500 })
    };
  }
}

// Register the document processing processor
// This should ideally be done once when the application starts,
// but for Next.js API routes, placing it here ensures it's registered
// when the route module is loaded.
if (!documentProcessingQueue.isProcessorRegistered('documentProcessing')) {
    documentProcessingQueue.registerProcessor('documentProcessing', async (job: DocumentProcessingJob) => {
    console.log(`Processing document job ID ${job.id} for user: ${job.userId}`);

    // Create a strategic director agent for the user associated with the job
    const strategicDirector = new StrategicDirectorAgent(job.userId); // Assuming constructor takes userId

    // Process the document query
    const result = await strategicDirector.queryDocumentsEnhanced(
        job.documentQuery,
        job.category,
        job.filename,
        job.namespace,
        job.useInternetSearch || false, // Default to false if not provided
        job.modelName
    );

    console.log(`Finished processing document job ID ${job.id} for user: ${job.userId}`);
    return result;
    });
}


/**
 * POST /api/document-processing
 * Submit a document processing job
 */
export async function POST(request: NextRequest) {
  try {
    console.log('Received POST /api/document-processing request');

    const authContext = await authenticateAndGetActor(request);
    if (authContext.response) {
      console.log('Authentication failed for POST /api/document-processing, returning error response');
      return authContext.response;
    }

    const actorId = authContext.actor!.id;
    console.log(`Request authenticated for POST /api/document-processing as user: ${actorId}`);

    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('Failed to parse request body for POST /api/document-processing:', parseError);
      return NextResponse.json({ error: 'Invalid JSON payload provided.' }, { status: 400 });
    }

    if (!body.documentQuery || typeof body.documentQuery !== 'string' || body.documentQuery.trim() === '') {
      console.warn('Missing or invalid required field: documentQuery');
      return NextResponse.json({ error: 'Missing or invalid required field: documentQuery' }, { status: 400 });
    }

    const jobData: Omit<DocumentProcessingJob, 'id' | 'status' | 'createdAt' | 'updatedAt' | 'progress' | 'attempts' | 'result' | 'error' | 'startedAt' | 'completedAt'> = {
      userId: actorId,
      documentQuery: body.documentQuery,
      category: body.category,
      filename: body.filename,
      namespace: body.namespace,
      useInternetSearch: body.useInternetSearch || false,
      modelName: body.modelName || 'google/gemini-pro', // Default model if not specified
      priority: body.priority || JobPriority.NORMAL,
      callbackUrl: body.callbackUrl,
      callbackHeaders: body.callbackHeaders
    };

    console.log(`Enqueueing document processing job for user ${actorId}, namespace: ${body.namespace || 'default'}`);

    const job = documentProcessingQueue.enqueue(jobData);
    console.log(`Job created successfully with ID: ${job.id} for user: ${actorId}`);

    return NextResponse.json({
      jobId: job.id,
      status: job.status,
      message: 'Document processing job submitted successfully.'
    }, { status: 202 });
  } catch (error) {
    console.error('Error submitting document processing job:', error);

    // Check for specific error types
    if (error instanceof Error) {
      if (error.message.includes('authentication') || error.message.includes('auth')) {
        console.error('Authentication error detected:', error.message);
        return NextResponse.json({
          error: 'Authentication error: ' + error.message,
          details: 'Please check your authentication credentials and try again.',
          code: 'AUTH_ERROR'
        }, { status: 401 });
      } else if (error.message.includes('processor') || error.message.includes('queue')) {
        console.error('Document processing queue error:', error.message);
        return NextResponse.json({
          error: 'Document processing system error: ' + error.message,
          details: 'The document processing system is currently unavailable. Please try again later.',
          code: 'QUEUE_ERROR'
        }, { status: 503 });
      }

      // Generic error with stack trace for debugging
      return NextResponse.json({
        error: error.message,
        details: error.stack,
        code: 'GENERAL_ERROR'
      }, { status: 500 });
    }

    // Unknown error type
    return NextResponse.json({
      error: 'An unknown error occurred while submitting the document processing job.',
      code: 'UNKNOWN_ERROR'
    }, { status: 500 });
  }
}

/**
 * GET /api/document-processing/:id
 * Get the status of a document processing job
 */
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const jobId = params.id;
    console.log(`Received GET /api/document-processing/${jobId} request`);

    if (!jobId) {
      return NextResponse.json({ error: 'Missing job ID in path parameter.' }, { status: 400 });
    }

    const authContext = await authenticateAndGetActor(request);
    if (authContext.response) {
      return authContext.response;
    }
    const actorId = authContext.actor!.id;
    console.log(`Request authenticated for GET /api/document-processing/${jobId} as user: ${actorId}`);

    const job = documentProcessingQueue.getJob(jobId);
    if (!job) {
      console.warn(`Job not found with ID: ${jobId}`);
      return NextResponse.json({ error: 'Job not found.' }, { status: 404 });
    }

    // Authorization: Check if the actor is allowed to view this job.
    // Allow if job.userId matches actorId or if the job was for an 'anonymous' actor (e.g. system job not tied to specific user)
    // Modify this logic if 'anonymous' jobs should also be restricted.
    if (job.userId !== actorId && job.userId !== 'anonymous') {
      console.warn(`User ${actorId} (authenticated) attempted to access job ${jobId} belonging to ${job.userId}.`);
      return NextResponse.json({ error: 'Forbidden. You do not have permission to access this job.' }, { status: 403 });
    }

    const responsePayload: any = {
      jobId: job.id,
      status: job.status,
      createdAt: job.createdAt,
      updatedAt: job.updatedAt,
      startedAt: job.startedAt,
      completedAt: job.completedAt,
      progress: job.progress,
      attempts: job.attempts,
      userId: job.userId, // Include userId for clarity
    };

    if (job.status === JobStatus.COMPLETED) {
      responsePayload.result = job.result;
    } else if (job.status === JobStatus.FAILED) {
      responsePayload.error = job.error;
    }

    return NextResponse.json(responsePayload);
  } catch (error) {
    console.error(`Error getting document processing job status for ID ${params.id}:`, error);

    // Check for specific error types
    if (error instanceof Error) {
      if (error.message.includes('authentication') || error.message.includes('auth')) {
        console.error('Authentication error detected:', error.message);
        return NextResponse.json({
          error: 'Authentication error: ' + error.message,
          details: 'Please check your authentication credentials and try again.',
          code: 'AUTH_ERROR'
        }, { status: 401 });
      }

      // Generic error with stack trace for debugging
      return NextResponse.json({
        error: error.message,
        details: error.stack,
        code: 'GENERAL_ERROR'
      }, { status: 500 });
    }

    // Unknown error type
    return NextResponse.json({
      error: 'An unknown error occurred while retrieving the document processing job.',
      code: 'UNKNOWN_ERROR'
    }, { status: 500 });
  }
}

/**
 * DELETE /api/document-processing/:id
 * Cancel a document processing job
 */
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const jobId = params.id;
    console.log(`Received DELETE /api/document-processing/${jobId} request`);

    if (!jobId) {
      return NextResponse.json({ error: 'Missing job ID in path parameter.' }, { status: 400 });
    }

    const authContext = await authenticateAndGetActor(request);
    if (authContext.response) {
      return authContext.response;
    }
    const actorId = authContext.actor!.id;
    console.log(`Request authenticated for DELETE /api/document-processing/${jobId} as user: ${actorId}`);

    const job = documentProcessingQueue.getJob(jobId);
    if (!job) {
      console.warn(`Job not found with ID: ${jobId} for cancellation.`);
      return NextResponse.json({ error: 'Job not found.' }, { status: 404 });
    }

    if (job.userId !== actorId && job.userId !== 'anonymous') {
      console.warn(`User ${actorId} attempted to cancel job ${jobId} belonging to ${job.userId}.`);
      return NextResponse.json({ error: 'Forbidden. You do not have permission to cancel this job.' }, { status: 403 });
    }

    if ([JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED].includes(job.status)) {
        console.log(`Cannot cancel job ${jobId} with status: ${job.status}.`);
        return NextResponse.json({
            error: `Job cannot be cancelled. Current status: ${job.status}.`
        }, { status: 400 });
    }

    console.log(`Attempting to cancel job ID: ${jobId}`);
    const cancelled = await documentProcessingQueue.cancelJob(jobId); // Assuming cancelJob is async

    const updatedJob = documentProcessingQueue.getJob(jobId); // Get the job again to reflect its final state

    if (!cancelled || !updatedJob || updatedJob.status !== JobStatus.CANCELLED) {
      console.warn(`Job ${jobId} could not be reliably cancelled. Current status: ${updatedJob?.status || 'unknown'}. Cancel attempt result: ${cancelled}`);
      return NextResponse.json({
        error: `Job could not be cancelled. It may have already completed or failed. Current status: ${updatedJob?.status || 'unknown'}.`,
        jobId: jobId,
        status: updatedJob?.status || 'unknown'
      }, { status: 409 }); // 409 Conflict - state conflict
    }

    console.log(`Job ${jobId} successfully cancelled. Final status: ${updatedJob.status}`);
    return NextResponse.json({
      jobId,
      status: updatedJob.status,
      message: 'Document processing job cancelled successfully.'
    });
  } catch (error) {
    console.error(`Error cancelling document processing job for ID ${params.id}:`, error);

    // Check for specific error types
    if (error instanceof Error) {
      if (error.message.includes('authentication') || error.message.includes('auth')) {
        console.error('Authentication error detected:', error.message);
        return NextResponse.json({
          error: 'Authentication error: ' + error.message,
          details: 'Please check your authentication credentials and try again.',
          code: 'AUTH_ERROR'
        }, { status: 401 });
      } else if (error.message.includes('not found') || error.message.includes('does not exist')) {
        return NextResponse.json({
          error: 'Job not found: ' + error.message,
          code: 'JOB_NOT_FOUND'
        }, { status: 404 });
      }

      // Generic error with stack trace for debugging
      return NextResponse.json({
        error: error.message,
        details: error.stack,
        code: 'GENERAL_ERROR'
      }, { status: 500 });
    }

    // Unknown error type
    return NextResponse.json({
      error: 'An unknown error occurred while cancelling the document processing job.',
      code: 'UNKNOWN_ERROR'
    }, { status: 500 });
  }
}