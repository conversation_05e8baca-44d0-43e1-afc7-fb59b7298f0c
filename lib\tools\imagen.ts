/**
 * Google Imagen integration for image generation
 * Supports Firebase job integration for advanced storage
 */

import { GoogleGenAI } from "@google/genai";
import { v4 as uuidv4 } from 'uuid';

// Define interfaces for Imagen processing
export interface ImagenGenerationOptions {
  prompt: string;
  model?: string;
  numberOfImages?: number;
  userId?: string; // Added userId for Firebase integration
  jobId?: string;  // Optional job ID for Firebase integration
}

export interface ImagenGenerationResult {
  imageUrl: string;
  jobId: string;
  namespace: string;
  base64Image?: string; // Added base64Image for Firebase storage
}

/**
 * Generate an image with Google's Imagen model
 * @param options - Generation options
 * @returns The generated image data
 */
export async function generateWithImagen(options: ImagenGenerationOptions): Promise<ImagenGenerationResult> {
  try {
    const {
      prompt,
      model = 'imagen-3.0-generate-002',
      numberOfImages = 1,
      userId,
      jobId = `imagen-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`,
    } = options;

    console.log(`Generating image with Google Imagen model: ${model}`);

    // Check if API key is configured
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error("Google API key is not configured. Please set the GOOGLE_API_KEY or GEMINI_API_KEY environment variable.");
    }
    console.log("Using Gemini API key:", apiKey.substring(0, 5) + "..." + apiKey.substring(apiKey.length - 5));

    // Initialize the Google GenAI client
    const ai = new GoogleGenAI({ apiKey });

    // Generate the image
    console.log(`Sending request to Google Imagen API with model: ${model}, prompt: ${prompt.substring(0, 50)}...`);

    let response;
    try {
      response = await ai.models.generateImages({
        model,
        prompt,
        config: {
          numberOfImages,
        },
      });
      console.log('Received response from Google Imagen API:', JSON.stringify(response).substring(0, 200));
    } catch (apiError: any) {
      console.error('Google Imagen API error details:', {
        message: apiError.message,
        name: apiError.name,
        stack: apiError.stack?.substring(0, 500),
        details: apiError.details || 'No details available'
      });
      throw apiError;
    }

    // Check if we have any generated images
    if (!response.generatedImages || response.generatedImages.length === 0) {
      throw new Error("No images were generated");
    }

    // Get the first generated image
    const generatedImage = response.generatedImages[0];

    if (!generatedImage.image || !generatedImage.image.imageBytes) {
      throw new Error("Generated image data is missing");
    }

    // Get the base64 image data
    const imageBytes = generatedImage.image.imageBytes;

    // Create a data URL for direct display in browser
    const imageUrl = `data:image/png;base64,${imageBytes}`;

    // Generate a namespace for the image using full UUIDv4
    const uuid = uuidv4();
    const namespace = userId ? uuid : 'google-imagen';
    console.log(`Generated namespace for Imagen image: ${namespace}`);

    // If userId is provided, we would typically store the image in Firebase
    // This would be handled by the calling code, but we'll prepare the result
    return {
      imageUrl,
      jobId,
      namespace,
      base64Image: imageBytes // Include the base64 image data for Firebase storage
    };
  } catch (error: any) {
    console.error("Error generating image with Google Imagen:", error);
    throw new Error(`Error from Google Imagen: ${error.message || "Unknown error"}`);
  }
}

/**
 * Process and store an Imagen-generated image in Firebase
 * This is a placeholder function that would be implemented in a real application
 * @param base64Image - The base64-encoded image data
 * @param userId - The user ID for Firebase storage
 * @param jobId - The job ID for the image
 * @returns The URL of the stored image
 */
export async function storeImagenImageInFirebase(
  _base64Image: string, // Prefixed with underscore to indicate it's intentionally unused
  userId: string,
  jobId: string
): Promise<string> {
  try {
    console.log(`Storing Imagen image in Firebase for user ${userId} with job ID ${jobId}`);

    // In a real implementation, this would upload the image to Firebase Storage
    // and update the job status in Firestore
    // The base64Image parameter would be used to store the actual image data

    // For now, we'll just return a mock URL
    return `https://storage.googleapis.com/user-images/${userId}/generated/${jobId}.png`;
  } catch (error: any) {
    console.error("Error storing Imagen image in Firebase:", error);
    throw new Error(`Failed to store image: ${error.message || "Unknown error"}`);
  }
}

/**
 * Get available Imagen models
 * @returns List of available models
 */
export function getImagenModels(): string[] {
  return [
    'imagen-3.0-generate-002',
    'imagen-3.0-generate-001',
  ];
}