import { NextRequest, NextResponse } from 'next/server';
import { adminDb, adminStorage } from 'components/firebase-admin';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/api/auth/[...nextauth]/authOptions';

export async function DELETE(
  req: NextRequest,
  { params }: { params: { projectId: string; documentId: string } }
) {
  console.log(`[PROJECT DOC DELETE] Starting document deletion process`);
  try {
    // Get the current session
    const session = await getServerSession(authOptions);
    console.log(`[PROJECT DOC DELETE] Session user: ${session?.user?.email || 'No user found'}`);

    if (!session?.user?.email) {
      console.log(`[PROJECT DOC DELETE] Unauthorized - No valid session`);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { projectId, documentId } = params;
    console.log(`[PROJECT DOC DELETE] Project ID: ${projectId}, Document ID: ${documentId}`);

    if (!projectId || !documentId) {
      console.log(`[PROJECT DOC DELETE] Missing project ID or document ID`);
      return NextResponse.json({ error: 'Missing project ID or document ID' }, { status: 400 });
    }

    // Get the document reference
    const docRef = adminDb.collection('projects').doc(projectId).collection('documents').doc(documentId);

    // Get the document data to find the storage path
    const docSnapshot = await docRef.get();

    if (!docSnapshot.exists) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 });
    }

    const docData = docSnapshot.data();

    // Check if the user has access to this project
    const projectRef = adminDb.collection('projects').doc(projectId);
    const projectDoc = await projectRef.get();

    if (!projectDoc.exists) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }

    const projectData = projectDoc.data();
    const userEmail = session.user.email;

    // Check if user is system admin
    const isSysAdmin = userEmail === '<EMAIL>';

    // Check if user is the project owner
    const isOwner = projectData?.owner === userEmail;

    // Check if user is a project member
    // First get the user's ID
    const userQuery = await adminDb.collection('users')
      .where('email', '==', userEmail)
      .limit(1)
      .get();

    let isMember = false;
    if (!userQuery.empty) {
      const userId = userQuery.docs[0].id;
      isMember = projectData?.members && Array.isArray(projectData.members) && projectData.members.includes(userId);
    }

    // Check if the current user is authorized to delete this document
    // Allow deletion if the user is the document creator or if they're the SYS_ADMIN or project owner
    const isCreator = docData?.createdBy === userEmail;

    if (!isSysAdmin && !isOwner && !isCreator) {
      return NextResponse.json({ error: 'You are not authorized to delete this document' }, { status: 403 });
    }

    // Get the namespace/file ID from the document data
    const namespace = docData?.namespace || docData?.fileId || documentId;
    console.log(`[PROJECT DOC DELETE] Using namespace: ${namespace}`);

    // For document processing, we need to use the fixed SYS_ADMIN user
    const userId = "<EMAIL>";
    console.log(`[PROJECT DOC DELETE] Using fixed admin user: ${userId}`);

    // Use the deleteDocumentAndChats API to properly clean up all related data
    // This will delete:
    // 1. The document metadata from Firestore
    // 2. The file from Firebase Storage
    // 3. The document chunks from Firestore
    // 4. The embeddings from Pinecone
    // 5. Any associated chats and messages

    try {
      // Call the deleteDocumentAndChats API to properly clean up all related data
      console.log(`[PROJECT DOC DELETE] Calling deleteDocumentAndChats API`);
      const deleteUrl = `${req.nextUrl.origin}/api/deleteDocumentAndChats`;
      console.log(`[PROJECT DOC DELETE] API URL: ${deleteUrl}`);

      const deletePayload = {
        userId: userId,
        namespace: namespace,
      };
      console.log(`[PROJECT DOC DELETE] API payload: ${JSON.stringify(deletePayload)}`);

      const deleteResponse = await fetch(deleteUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(deletePayload),
      });

      if (!deleteResponse.ok) {
        const errorText = await deleteResponse.text();
        console.log(`[PROJECT DOC DELETE] API returned error: ${deleteResponse.status}: ${errorText}`);
        throw new Error(`API returned ${deleteResponse.status}: ${errorText}`);
      }

      console.log(`[PROJECT DOC DELETE] API call successful`);
    } catch (deleteError) {
      console.error('[PROJECT DOC DELETE] Error using deleteDocumentAndChatsByNamespace:', deleteError);

      // Fallback to basic deletion if the comprehensive deletion fails
      console.log('[PROJECT DOC DELETE] Falling back to basic deletion...');

      // Delete the file from storage if path exists
      if (docData?.path) {
        console.log(`[PROJECT DOC DELETE] Deleting file from storage: ${docData.path}`);
        const bucket = adminStorage.bucket();
        const file = bucket.file(docData.path);
        try {
          await file.delete();
          console.log(`[PROJECT DOC DELETE] File deleted from storage`);
        } catch (storageError) {
          console.error(`[PROJECT DOC DELETE] Error deleting file from storage:`, storageError);
          // Continue with Firestore deletion even if storage deletion fails
        }
      }

      // Delete the document from Firestore
      console.log(`[PROJECT DOC DELETE] Deleting document from Firestore`);
      await docRef.delete();
      console.log(`[PROJECT DOC DELETE] Document deleted from Firestore`);
    }

    console.log(`[PROJECT DOC DELETE] Document deletion completed successfully`);
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('[PROJECT DOC DELETE] Error deleting document:', error);
    return NextResponse.json(
      { error: `Failed to delete document: ${error.message}` },
      { status: 500 }
    );
  }
}
