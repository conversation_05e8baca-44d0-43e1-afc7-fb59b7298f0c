/**
 * LLM Service Adapter
 * 
 * This adapter wraps various LLM services to provide a consistent interface
 * for the PMOAssessmentAgent to use.
 */

import { simplePromptOptimizer } from './simplePromptOptimizer';
import { processWithOpenAI } from './openai-ai';
import { processWithAnthropic } from './anthropic-ai';
import { processWithGroq } from './groq-ai';
import { processWithGoogleAI } from './google-ai';

// Define the response interface
export interface LlmResponse {
  success: boolean;
  generatedText?: string;
  error?: string;
}

// Define the LLM service interface
export interface LlmService {
  generateText(params: {
    prompt: string;
    modelOptions: {
      temperature: number;
      maxTokens: number;
      model?: string;
    };
  }): Promise<LlmResponse>;
}

/**
 * LLM Service Adapter class that implements the LlmService interface
 * and delegates to the appropriate LLM service based on the model provider.
 */
export class LlmServiceAdapter implements LlmService {
  private modelProvider: string;

  constructor(modelProvider: string = 'openai') {
    this.modelProvider = modelProvider;
  }

  /**
   * Generate text using the configured LLM service
   * @param params - Parameters for text generation
   * @returns - Generated text response
   */
  async generateText(params: {
    prompt: string;
    modelOptions: {
      temperature: number;
      maxTokens: number;
      model?: string;
    };
  }): Promise<LlmResponse> {
    const { prompt, modelOptions } = params;
    const { temperature, maxTokens, model } = modelOptions;

    try {
      let response: string;

      // Select the appropriate LLM service based on the model provider
      switch (this.modelProvider.toLowerCase()) {
        case 'openai':
          response = await processWithOpenAI({
            prompt,
            model: model || 'gpt-4o',
            modelOptions: {
              temperature,
              maxTokens
            }
          });
          break;

        case 'anthropic':
          response = await processWithAnthropic({
            prompt,
            model: model || 'claude-3-7-sonnet-latest',
            modelOptions: {
              temperature,
              maxTokens
            }
          });
          break;

        case 'groq':
          response = await processWithGroq({
            prompt,
            model: model || 'deepseek-r1-distill-llama-70b',
            modelOptions: {
              temperature,
              maxTokens
            }
          });
          break;

        case 'google':
          response = await processWithGoogleAI({
            prompt,
            model: model || 'gemini-1.5-pro'
          });
          break;

        default:
          // Fallback to simplePromptOptimizer
          const optimizerResult = await simplePromptOptimizer.optimizePrompt({
            originalPrompt: prompt,
            includeExplanation: false,
            modelOptions: {
              temperature,
              maxTokens
            }
          });

          if (!optimizerResult.success) {
            throw new Error(optimizerResult.error || 'Failed to optimize prompt');
          }

          response = optimizerResult.optimizedPrompt;
      }

      return {
        success: true,
        generatedText: response
      };
    } catch (error) {
      console.error('Error in LlmServiceAdapter:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
}

// Export a factory function to create an LlmServiceAdapter
export function createLlmService(modelProvider: string = 'openai'): LlmService {
  return new LlmServiceAdapter(modelProvider);
}
