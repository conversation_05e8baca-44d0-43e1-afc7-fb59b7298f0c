import { processWithGroq } from '../tools/groq-ai';
import { adminDb } from '../../components/firebase-admin';
import { addTask } from '../../app/lib/firebase/planner';
import { Task, TaskPriority, TaskStatus } from '../../admin/planner/types';
import { CalendarTool } from '../tools/calendarTool';
import { z } from 'zod';

export interface PMOProjectsTaskAgentOptions {
  includeExplanation?: boolean;
  streamResponse?: boolean;
  onStreamUpdate?: (update: PMOTaskStreamUpdate) => void;
}

export interface PMOTaskStreamUpdate {
  stage: 'retrieving-output' | 'extracting-tasks' | 'creating-tasks' | 'complete';
  data?: any;
  message?: string;
}

export interface PMOProjectsTaskAgentResult {
  success: boolean;
  tasksCreated: Array<{
    taskId: string;
    title: string;
    projectId: string;
    description: string;
  }>;
  error?: string;
  analysis?: string;
  totalTasksExtracted: number;
}

// Schema for extracted task data
const ExtractedTaskSchema = z.object({
  title: z.string(),
  description: z.string(),
  category: z.string(),
  priority: z.enum(['Low', 'Medium', 'High']).default('High'),
  estimatedDuration: z.string().optional(), // e.g., "3 days", "1 week"
  dependencies: z.array(z.string()).optional(),
  notes: z.string().optional(),
  dueDate: z.string().optional(), // YYYY-MM-DD format
  startDate: z.string().optional(), // YYYY-MM-DD format
  assignedTo: z.array(z.string()).optional(), // Team assignments from Strategic Analysis
  teamAssignment: z.string().optional() // Original team name from Strategic Analysis
});

const ExtractedTasksResponseSchema = z.object({
  tasks: z.array(ExtractedTaskSchema),
  analysis: z.string(),
  confidence: z.number().min(0).max(1),
  projectContext: z.string()
});

type ExtractedTask = z.infer<typeof ExtractedTaskSchema>;
type ExtractedTasksResponse = z.infer<typeof ExtractedTasksResponseSchema>;

export class PMOProjectsTaskAgent {
  private options: PMOProjectsTaskAgentOptions;
  private calendarTool: CalendarTool;

  constructor(options: PMOProjectsTaskAgentOptions = {}) {
    this.options = {
      includeExplanation: true,
      streamResponse: false,
      ...options
    };
    this.calendarTool = new CalendarTool();
  }

  // Removed the first duplicate implementation of extractTasksFromAgentOutput

  /**
   * Extract tasks from Strategic Director Agent output (for preview - does NOT create tasks)
   */
  async extractTasksFromAgentOutput(
    requestId: string,
    projectName: string,
    projectDescription: string
  ): Promise<{
    success: boolean;
    tasksCreated: Array<{
      title: string;
      description: string;
      category: string;
      priority: TaskPriority; // Changed from 'High' | 'Medium' | 'Low'
      dueDate: Date;
      startDate: Date;
      estimatedDuration?: string;
      dependencies?: string[];
      notes?: string;
      assignedTo?: string[]; // Team assignments from Strategic Analysis
      teamAssignment?: string; // Original team name from Strategic Analysis
      subtasks?: Array<{
        title: string;
        description: string;
        priority: TaskPriority; // Changed from 'High' | 'Medium' | 'Low'
        dueDate?: Date;
        estimatedDuration?: string;
      }>;
    }>;
    error?: string;
    analysis?: string;
  }> {
    try {
      console.log(`PMOProjectsTaskAgent: Extracting tasks for preview from requestId: ${requestId}`);

      // Step 1: Retrieve the agent output
      const agentOutput = await this._getAgentOutput(requestId);

      if (!agentOutput) {
        throw new Error(`Agent output not found for requestId: ${requestId}`);
      }

      // Step 2: Extract tasks using Groq deepseek LLM
      const extractedTasks = await this._extractTasksWithGroq(agentOutput, projectName, projectDescription);

      if (!extractedTasks.tasks || extractedTasks.tasks.length === 0) {
        return {
          success: false,
          tasksCreated: [],
          error: 'No tasks found in the agent output',
          analysis: extractedTasks.analysis
        };
      }

      // Step 3: Format tasks for preview (with calculated dates and subtasks)
      const formattedTasks = [];
      for (const task of extractedTasks.tasks) {
        const startDate = task.startDate ? new Date(task.startDate) : new Date();
        const dueDate = task.dueDate ? new Date(task.dueDate) : await this._calculateDueDate(startDate, task.estimatedDuration);

        // Subtasks are not currently generated - this is a placeholder for future enhancement
        const subtasks: Array<{
          title: string;
          description: string;
          priority: TaskPriority;
          dueDate?: Date;
          estimatedDuration?: string;
        }> = [];

        formattedTasks.push({
          title: task.title,
          description: task.description,
          category: task.category,
          priority: 'High' as TaskPriority, // Consistent with class description & TaskPriority type
          dueDate,
          startDate,
          estimatedDuration: task.estimatedDuration,
          dependencies: task.dependencies,
          notes: task.notes,
          assignedTo: task.assignedTo,
          teamAssignment: task.teamAssignment,
          subtasks
        });
      }

      return {
        success: true,
        tasksCreated: formattedTasks,
        analysis: extractedTasks.analysis
      };

    } catch (error) {
      console.error('PMOProjectsTaskAgent: Error extracting tasks for preview:', error);
      return {
        success: false,
        tasksCreated: [],
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Create tasks for a project from Strategic Director Agent output
   */
  async createTasksFromAgentOutput(
    requestId: string,
    projectId: string,
    projectName: string,
    projectDescription: string
  ): Promise<PMOProjectsTaskAgentResult> {
    try {
      console.log(`PMOProjectsTaskAgent: Starting task creation for project ${projectId} from requestId: ${requestId}`);

      // Step 1: Retrieve the agent output
      this._streamUpdate('retrieving-output', null, 'Retrieving agent output from Firebase...');
      const agentOutput = await this._getAgentOutput(requestId);

      if (!agentOutput) {
        throw new Error(`Agent output not found for requestId: ${requestId}`);
      }

      // Step 2: Extract tasks using Groq deepseek LLM
      this._streamUpdate('extracting-tasks', null, 'Extracting tasks using Groq deepseek LLM...');
      const extractedTasks = await this._extractTasksWithGroq(agentOutput, projectName, projectDescription);

      if (!extractedTasks.tasks || extractedTasks.tasks.length === 0) {
        return {
          success: false,
          tasksCreated: [],
          error: 'No tasks found in the agent output',
          analysis: extractedTasks.analysis,
          totalTasksExtracted: 0
        };
      }

      // Step 3: Create tasks in Firebase
      this._streamUpdate('creating-tasks', null, `Creating ${extractedTasks.tasks.length} tasks...`);
      const createdTasks = await this._createTasks(extractedTasks.tasks, projectId, agentOutput);

      this._streamUpdate('complete', null, 'Task creation completed successfully');

      return {
        success: true,
        tasksCreated: createdTasks,
        analysis: extractedTasks.analysis,
        totalTasksExtracted: extractedTasks.tasks.length
      };

    } catch (error) {
      console.error('PMOProjectsTaskAgent: Error creating tasks:', error);
      return {
        success: false,
        tasksCreated: [],
        error: error instanceof Error ? error.message : String(error),
        totalTasksExtracted: 0
      };
    }
  }

  /**
   * Retrieve agent output from Firestore
   */
  private async _getAgentOutput(requestId: string): Promise<any> {
    try {
      const doc = await adminDb.collection('Agent_Output').doc(requestId).get();

      if (!doc.exists) {
        throw new Error(`Agent output document not found: ${requestId}`);
      }

      return doc.data();
    } catch (error) {
      console.error('PMOProjectsTaskAgent: Error retrieving agent output:', error);
      throw error;
    }
  }

  /**
   * Extract tasks from agent output using Groq deepseek LLM
   */
  private async _extractTasksWithGroq(
    agentOutput: any,
    projectName: string,
    projectDescription: string
  ): Promise<ExtractedTasksResponse> {
    try {
      const outputContent = agentOutput.result?.output || agentOutput.result?.content || '';
      const thinkingContent = agentOutput.result?.thinking || '';
      const agentType = agentOutput.agentType || 'unknown';
      const category = agentOutput.category || agentOutput.pmoMetadata?.category || '';

      // Get current date using Calendar tool
      const currentDateResult = await this.calendarTool.process({
        operation: 'getCurrentDateTime',
        dateFormat: 'yyyy-MM-dd'
      });

      const todayDate = currentDateResult.success ? currentDateResult.result as string : new Date().toISOString().split('T')[0];

      // Enhanced prompt for PMO task extraction with special handling for numbered task lists
      const isPMOContext = category.toLowerCase().includes('pmo') || agentType.toLowerCase().includes('strategic');

      console.log(`PMOProjectsTaskAgent: Task extraction mode - PMO Context: ${isPMOContext}, Agent Type: ${agentType}, Category: ${category}`);

      const prompt = `
You are an advanced Task Extraction AI specialized in analyzing Strategic Director Agent outputs to extract specific, actionable tasks for project implementation.

**CURRENT DATE:** ${todayDate}

CONTEXT:
Project Name: ${projectName}
Project Description: ${projectDescription}
Agent Type: ${agentType}
Category: ${category}
PMO Context: ${isPMOContext ? 'YES - Enhanced PMO task extraction mode' : 'NO'}

AGENT OUTPUT TO ANALYZE:
Output Content: ${outputContent}
Strategic Thinking: ${thinkingContent}

${isPMOContext ? `
**PMO TASK EXTRACTION MODE ACTIVATED**
CRITICAL INSTRUCTIONS FOR PMO CONTENT:

🎯 **PRIMARY FOCUS: TEAM ASSIGNMENTS SECTION**
1. **FIRST PRIORITY**: Search for sections titled "TEAM ASSIGNMENTS", "4. TEAM ASSIGNMENTS", "ASSIGNMENTS", or similar
2. **EXACT EXTRACTION**: Extract tasks EXACTLY as they appear in the TEAM ASSIGNMENTS section
3. **PRESERVE FORMAT**: Maintain the exact task titles, descriptions, and team assignments from the Strategic Analysis
4. **TEAM MAPPING**: Look for patterns like:
   - "Task 1 – [Task Title] • Assigned to : [Team Name]"
   - "**Task**: [Description] **Assigned to**: [Team Name]"
   - "• [Task Description] (Research Team)"
   - "[Task Number] – [Task Title] • Assigned to : [Team Name] • Rationale : [Reason]"

🔍 **SECTION SCANNING PRIORITY ORDER**:
1. "TEAM ASSIGNMENTS" or "4. TEAM ASSIGNMENTS" (HIGHEST PRIORITY)
2. "ASSIGNMENTS (all deadlines are from campaign Day 0)"
3. "Task 1 –", "Task 2 –", "Task 3 –" numbered lists
4. Sections with "Assigned to:" patterns
5. "Strategic Reasoning", "Implementation", "Action Items" (LOWER PRIORITY)

📋 **EXTRACTION RULES**:
- If TEAM ASSIGNMENTS section exists, extract ALL tasks from it first
- Preserve team names EXACTLY: "Research Team", "Marketing Team", "Software Design Team", etc.
- Keep task numbering and structure from the original
- Include rationale and dependencies if mentioned
- Only create additional tasks if TEAM ASSIGNMENTS section is incomplete or missing
- DO NOT modify or rephrase tasks that are already well-defined in TEAM ASSIGNMENTS

🚨 **CRITICAL**: The TEAM ASSIGNMENTS section contains the authoritative task list. Extract these tasks with 100% fidelity.
` : ''}

TASK EXTRACTION REQUIREMENTS:
1. Extract SPECIFIC, ACTIONABLE tasks that can be assigned to team members
2. Each task should be concrete and measurable
3. Focus on implementation steps, not high-level strategies
4. Include tasks for planning, execution, monitoring, and evaluation phases
5. Ensure tasks are realistic and achievable
6. PRESERVE team assignments from the Strategic Analysis (e.g., "Research Team", "Marketing Team", "Software Design Team")
7. Look for phrases like "Assigned to:", "Team:", or team names mentioned with tasks
${isPMOContext ? '8. For PMO contexts: Extract ALL numbered/listed tasks and convert strategic items to actionable tasks' : ''}

RESPONSE FORMAT: Return a valid JSON object with this exact structure:
{
  "tasks": [
    {
      "title": "Specific, actionable task title (max 80 chars)",
      "description": "Detailed description of what needs to be done, including deliverables and success criteria",
      "category": "Marketing|Design|Research|Sales|Admin|Development|Operations",
      "priority": "High",
      "estimatedDuration": "X days/weeks",
      "dependencies": ["Task title that must be completed first"],
      "notes": "Additional context or requirements",
      "dueDate": "YYYY-MM-DD (realistic deadline based on ${todayDate})",
      "startDate": "YYYY-MM-DD (use ${todayDate} or logical start date)",
      "teamAssignment": "Team name from Strategic Analysis (e.g., 'Research Team', 'Marketing Team', 'Software Design Team')",
      "assignedTo": []
    }
  ],
  "analysis": "Brief explanation of how tasks were extracted and their alignment with the strategic output",
  "confidence": 0.85,
  "projectContext": "Summary of the project context that influenced task extraction"
}

TASK EXTRACTION GUIDELINES:
${isPMOContext ? `
**PMO-SPECIFIC GUIDELINES:**
- **PRIORITY 1**: Extract ALL tasks from TEAM ASSIGNMENTS section (typically 3-8 tasks)
- **PRIORITY 2**: If TEAM ASSIGNMENTS incomplete, extract additional tasks to reach 8-15 total
- **PRESERVE EXACTLY**: Task titles, descriptions, and team assignments from TEAM ASSIGNMENTS
- **TEAM FIDELITY**: Maintain exact team names ("Research Team", "Marketing Team", etc.)
- **STRUCTURE**: Keep original task numbering and dependencies
- **PHASES**: Include tasks from Planning, Research, Development, Implementation, Testing, Launch, Evaluation
- **DELIVERABLES**: Pay attention to specific deliverables mentioned in the strategic analysis
- **BREAKDOWN**: Only break down complex items if TEAM ASSIGNMENTS section lacks detail
- **COMPLETENESS**: Ensure all tasks from TEAM ASSIGNMENTS are captured before adding others
` : `
**GENERAL GUIDELINES:**
- Extract 5-12 specific tasks (avoid too many or too few)
`}
- Task titles should be action-oriented (start with verbs like "Create", "Develop", "Analyze", "Implement")
- Descriptions should include specific deliverables and success criteria
- Categories should match the project's domain and strategic focus
- Dependencies should reference other task titles in the same extraction
- Start dates should be TODAY (${todayDate}) or logical future dates within 1-7 days
- Due dates should be realistic based on task complexity and dependencies (typically 3-30 days from start)
- Use YYYY-MM-DD format for all dates and consider weekends and realistic timelines
- Create a logical workflow sequence with proper task dependencies
- All tasks get HIGH priority as specified in requirements

${isPMOContext ? `
**PMO TASK EXAMPLES FROM TEAM ASSIGNMENTS SECTION:**
EXTRACT EXACTLY AS SHOWN:
- "Task 1 – Validate X, Y, Z Metrics • Assigned to : Research Team"
- "Task 2 – Competitive Analysis Report • Assigned to : Research Team"
- "Task 3 – Messaging Framework & Personas • Assigned to : Marketing Team"
- "Conduct comprehensive market research and competitor analysis (Research Team)"
- "Develop brand messaging framework and value proposition (Marketing Team)"
- "Create content strategy and editorial calendar (Marketing Team)"
- "Design and develop marketing website landing pages (Software Design Team)"

**PRESERVE TEAM ASSIGNMENTS EXACTLY:**
- Research Team → "Research Team"
- Marketing Team → "Marketing Team"
- Software Design Team → "Software Design Team"
- Sales Team → "Sales Team"
- Business Analysis Team → "Business Analysis Team"
` : `
**GENERAL TASK EXAMPLES:**
- "Create brand messaging framework document"
- "Develop social media content calendar for Q1"
- "Conduct competitor analysis and create comparison matrix"
- "Design landing page wireframes and mockups"
- "Implement email marketing automation workflow"
`}

If no clear tasks can be extracted, return an empty tasks array with appropriate analysis.
`;

      // Use Gemini for PMO contexts for better reasoning, Groq for others
      let response: string;
      if (isPMOContext) {
        console.log('PMOProjectsTaskAgent: Using Gemini for enhanced PMO task extraction');
        const { processWithGoogleAI } = await import('../../lib/tools/google-ai');
        response = await processWithGoogleAI({
          prompt,
          model: 'gemini-2.5-pro-preview-05-06'
        });
      } else {
        response = await processWithGroq({
          prompt,
          model: 'deepseek-r1-distill-llama-70b',
          modelOptions: {
            temperature: 0.3,
            max_tokens: 4000,
          },
        });
      }

      // Parse and validate the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error(`No valid JSON found in ${isPMOContext ? 'Gemini' : 'Groq'} response`);
      }

      const parsedResponse = JSON.parse(jsonMatch[0]);
      const validatedResponse = ExtractedTasksResponseSchema.parse(parsedResponse);

      console.log(`PMOProjectsTaskAgent: Successfully extracted ${validatedResponse.tasks.length} tasks using ${isPMOContext ? 'Gemini (PMO mode)' : 'Groq (standard mode)'}`);
      if (isPMOContext && validatedResponse.tasks.length > 0) {
        console.log(`PMOProjectsTaskAgent: PMO tasks extracted: ${validatedResponse.tasks.map(t => t.title).join(', ')}`);
      }

      // Trigger modal notification for successful task extraction
      this._triggerTaskExtractionModal(validatedResponse, isPMOContext, isPMOContext ? 'Gemini' : 'Groq', projectName, agentType);

      return validatedResponse;

    } catch (error) {
      console.error('PMOProjectsTaskAgent: Error extracting tasks:', error);
      throw new Error(`Failed to extract tasks: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Create tasks in Firebase using the addTask function
   */
  private async _createTasks(
    extractedTasks: ExtractedTask[],
    projectId: string,
    _agentOutput: any
  ): Promise<Array<{ taskId: string; title: string; projectId: string; description: string }>> {
    const createdTasks = [];

    for (const task of extractedTasks) {
      try {
        console.log(`PMOProjectsTaskAgent: Creating task: ${task.title}`);

        // Calculate dates
        const startDate = task.startDate ? new Date(task.startDate) : new Date();
        const dueDate = task.dueDate ? new Date(task.dueDate) : await this._calculateDueDate(startDate, task.estimatedDuration);

        // Determine task assignment based on team assignment or fallback to admin
        const taskAssignedTo = task.assignedTo && task.assignedTo.length > 0
          ? task.assignedTo
          : this._mapTeamToUsers(task.teamAssignment);

        console.log(`PMOProjectsTaskAgent: Task "${task.title}"`);
        console.log(`  - Original teamAssignment: "${task.teamAssignment}"`);
        console.log(`  - Mapped to users: [${taskAssignedTo.join(', ')}]`);
        console.log(`  - Task assignedTo field: [${task.assignedTo?.join(', ') || 'none'}]`);

        // Prepare task data according to Task interface
        const taskData: Omit<Task, 'id'> = {
          projectId,
          title: task.title,
          description: task.description,
          category: task.category,
          status: 'Not Started' as TaskStatus,
          startDate,
          dueDate,
          assignedTo: taskAssignedTo, // Use team-based assignment
          priority: 'High' as TaskPriority, // Always HIGH priority as specified
          dependencies: task.dependencies || [],
          notes: task.notes || `Generated from Strategic Director Agent output.
TEAM ASSIGNMENT: ${task.teamAssignment || 'ADMIN'}
${task.estimatedDuration ? `Estimated duration: ${task.estimatedDuration}` : ''}
Original team from Strategic Analysis: ${task.teamAssignment || 'Not specified'}`,
          createdBy: 'pmo-projects-task-agent',
          createdAt: new Date(),
          updatedAt: new Date()
        };

        // Create the task using the addTask function
        const taskId = await addTask(taskData);

        createdTasks.push({
          taskId,
          title: task.title,
          projectId,
          description: task.description
        });

        console.log(`PMOProjectsTaskAgent: Successfully created task ${task.title} with ID: ${taskId}`);

      } catch (error) {
        console.error(`PMOProjectsTaskAgent: Error creating task ${task.title}:`, error);
        // Continue with other tasks even if one fails
      }
    }

    return createdTasks;
  }

  /**
   * Map team names from Strategic Analysis to actual user assignments
   * NOTE: Currently all teams map to admin user, but team assignment is preserved in task notes
   */
  private _mapTeamToUsers(teamName?: string): string[] {
    const adminUser = '<EMAIL>';

    if (!teamName) {
      console.log(`PMOProjectsTaskAgent: No team name provided, defaulting to admin`);
      return [adminUser];
    }

    // Map team names to actual users
    // TODO: In the future, different teams could be mapped to different users
    const teamMappings: Record<string, string[]> = {
      'Research Team': [adminUser], // Future: could <NAME_EMAIL>
      'Marketing Team': [adminUser], // Future: could <NAME_EMAIL>
      'Software Design Team': [adminUser], // Future: could <NAME_EMAIL>
      'Sales Team': [adminUser], // Future: could <NAME_EMAIL>
      'Business Analysis Team': [adminUser], // Future: could <NAME_EMAIL>
      'ADMIN': [adminUser]
    };

    // Try exact match first
    if (teamMappings[teamName]) {
      console.log(`PMOProjectsTaskAgent: Exact team match found for "${teamName}" -> [${teamMappings[teamName].join(', ')}]`);
      return teamMappings[teamName];
    }

    // Try partial matches
    const lowerTeamName = teamName.toLowerCase();
    for (const [key, users] of Object.entries(teamMappings)) {
      if (key.toLowerCase().includes(lowerTeamName) || lowerTeamName.includes(key.toLowerCase())) {
        console.log(`PMOProjectsTaskAgent: Partial team match found for "${teamName}" -> "${key}" -> [${users.join(', ')}]`);
        return users;
      }
    }

    // Default to admin if no match found
    console.log(`PMOProjectsTaskAgent: No team mapping found for "${teamName}", defaulting to admin`);
    return [adminUser];
  }

  /**
   * Calculate due date based on start date and estimated duration using Calendar tool
   */
  private async _calculateDueDate(startDate: Date, estimatedDuration?: string): Promise<Date> {
    if (!estimatedDuration) {
      // Default to 7 days if no duration specified
      const result = await this.calendarTool.process({
        operation: 'calculateDate',
        date: startDate.toISOString().split('T')[0],
        daysToAdd: 7,
        dateFormat: 'yyyy-MM-dd'
      });

      return result.success ? new Date(result.result as string) : new Date(startDate.getTime() + 7 * 24 * 60 * 60 * 1000);
    }

    // Parse duration string (e.g., "3 days", "2 weeks", "1 month")
    const durationMatch = estimatedDuration.match(/(\d+)\s*(day|week|month)s?/i);
    if (durationMatch) {
      const amount = parseInt(durationMatch[1]);
      const unit = durationMatch[2].toLowerCase();

      let daysToAdd = 7; // Default fallback
      switch (unit) {
        case 'day':
          daysToAdd = amount;
          break;
        case 'week':
          daysToAdd = amount * 7;
          break;
        case 'month':
          daysToAdd = amount * 30; // Approximate month as 30 days
          break;
      }

      const result = await this.calendarTool.process({
        operation: 'calculateDate',
        date: startDate.toISOString().split('T')[0],
        daysToAdd,
        dateFormat: 'yyyy-MM-dd'
      });

      return result.success ? new Date(result.result as string) : new Date(startDate.getTime() + daysToAdd * 24 * 60 * 60 * 1000);
    } else {
      // If we can't parse the duration, default to 7 days
      const result = await this.calendarTool.process({
        operation: 'calculateDate',
        date: startDate.toISOString().split('T')[0],
        daysToAdd: 7,
        dateFormat: 'yyyy-MM-dd'
      });

      return result.success ? new Date(result.result as string) : new Date(startDate.getTime() + 7 * 24 * 60 * 60 * 1000);
    }
  }

  /**
   * Trigger task extraction success modal notification
   */
  private _triggerTaskExtractionModal(
    extractedTasks: ExtractedTasksResponse,
    isPMOContext: boolean,
    modelUsed: 'Gemini' | 'Groq',
    projectName: string,
    agentType: string
  ): void {
    try {
      // Calculate processing time (approximate)
      const processingTime = Math.round(Math.random() * 3 + 2); // 2-5 seconds simulation

      // Prepare task extraction result for modal
      const result = {
        taskCount: extractedTasks.tasks.length,
        extractionMode: isPMOContext ? 'PMO' as const : 'Standard' as const,
        modelUsed,
        tasks: extractedTasks.tasks.map(task => ({
          title: task.title,
          category: task.category,
          assignedTo: task.teamAssignment || 'ADMIN',
          dueDate: task.dueDate
        })),
        confidence: extractedTasks.confidence || 0.85,
        processingTime,
        projectName,
        agentType,
        requestId: `task-extraction-${Date.now()}`
      };

      // Use global notification system to trigger modal
      if (typeof window !== 'undefined') {
        // Client-side: dispatch custom event
        const event = new CustomEvent('taskExtractionSuccess', {
          detail: result
        });
        window.dispatchEvent(event);
        console.log('PMOProjectsTaskAgent: Dispatched taskExtractionSuccess event', result);
      } else {
        // Server-side: log for debugging
        console.log('PMOProjectsTaskAgent: Task extraction success (server-side)', result);
      }
    } catch (error) {
      console.error('PMOProjectsTaskAgent: Error triggering task extraction modal:', error);
    }
  }

  /**
   * Stream update helper
   */
  private _streamUpdate(stage: PMOTaskStreamUpdate['stage'], data?: any, message?: string) {
    if (this.options.streamResponse && this.options.onStreamUpdate) {
      this.options.onStreamUpdate({ stage, data, message });
    }
  }
}

// Export a default instance
export const pmoProjectsTaskAgent = new PMOProjectsTaskAgent({
  includeExplanation: true,
  streamResponse: false
});