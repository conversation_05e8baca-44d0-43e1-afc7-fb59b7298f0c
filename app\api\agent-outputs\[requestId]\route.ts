import { NextRequest, NextResponse } from 'next/server';
import { adminDb } from 'components/firebase-admin';

export async function GET(
  req: NextRequest,
  { params }: { params: { requestId: string } }
) {
  try {
    const requestId = params.requestId;
    
    // Fetch the specific output from Firestore
    const outputDoc = await adminDb.collection('Agent_Output').doc(requestId).get();
    
    if (!outputDoc.exists) {
      return NextResponse.json({ error: 'Output not found' }, { status: 404 });
    }
    
    const outputData = outputDoc.data();
    
    if (!outputData) {
      return NextResponse.json({ error: 'Output data is empty' }, { status: 500 });
    }
    
    return NextResponse.json(outputData);
  } catch (error) {
    console.error('Error fetching agent output:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Failed to fetch agent output' 
    }, { status: 500 });
  }
}

