import { NextRequest, NextResponse } from "next/server";
import { PdfGeneratorTool, PdfContent } from "../../../lib/tools/pdf-generator";

// Initialize the PDF generator tool
const pdfGeneratorTool = new PdfGeneratorTool();

interface LlmComparisonPdfRequest {
  title: string;
  criteria: string;
  optimizedPrompt: string;
  modelResponses: {
    model: string;
    provider: string;
    response: string | null;
    error: string | null;
  }[];
  assessment: string;
  consolidatedResponse: string | null;
}

/**
 * API route for generating a PDF from LLM comparison results
 */
export async function POST(request: NextRequest) {
  try {
    const {
      title = "LLM Comparison Results",
      criteria,
      optimizedPrompt,
      modelResponses,
      assessment,
      consolidatedResponse
    }: LlmComparisonPdfRequest = await request.json();

    // Validate required parameters
    if (!criteria || !modelResponses || !assessment) {
      return NextResponse.json({
        success: false,
        error: "Missing required parameters"
      }, { status: 400 });
    }

    // Create content sections for the PDF
    const pdfContents: PdfContent[] = [];

    // Add criteria section
    pdfContents.push({
      title: "Evaluation Criteria",
      content: `## Original Prompt\n\n${title}\n\n## Optimized Prompt\n\n${optimizedPrompt}\n\n## Criteria\n\n${criteria}`
    });

    // Add model responses
    modelResponses.forEach((modelResponse, index) => {
      pdfContents.push({
        title: `Model ${index + 1}: ${modelResponse.model} via ${modelResponse.provider}`,
        content: modelResponse.error 
          ? `**Error:** ${modelResponse.error}` 
          : modelResponse.response || "No response"
      });
    });

    // Add assessment
    pdfContents.push({
      title: "Assessment",
      content: assessment
    });

    // Add consolidated response if available
    if (consolidatedResponse) {
      pdfContents.push({
        title: "Consolidated Response",
        content: consolidatedResponse
      });
    }

    // Generate the PDF
    const pdfBuffer = await pdfGeneratorTool.generatePdf(pdfContents, {
      title: title,
      subtitle: "Multi-LLM Comparison Results",
      date: new Date().toLocaleDateString(),
      includeCover: true,
      includeToc: true,
      saveToByteStore: false // Don't save to byteStore, just return the buffer
    });

    // Return the PDF as a downloadable file
    return new NextResponse(pdfBuffer as Buffer, {
      headers: {
        "Content-Type": "application/pdf",
        "Content-Disposition": `attachment; filename="llm-comparison-${Date.now()}.pdf"`
      }
    });
  } catch (error: any) {
    console.error("Error generating PDF:", error);
    return NextResponse.json({
      success: false,
      error: error.message || "An error occurred while generating the PDF"
    }, { status: 500 });
  }
}