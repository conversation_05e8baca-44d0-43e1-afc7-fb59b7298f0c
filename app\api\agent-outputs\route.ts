import { NextRequest, NextResponse } from 'next/server';
import { adminDb } from '../../../components/firebase/admin';

export async function GET(req: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(req.url);
    const requestId = url.searchParams.get('requestId');
    const limit = parseInt(url.searchParams.get('limit') || '10', 10);
    const page = parseInt(url.searchParams.get('page') || '1', 10);

    // If requestId is provided, get a specific document
    if (requestId) {
      const docRef = adminDb.collection('Agent_Output').doc(requestId);
      const doc = await docRef.get();

      if (!doc.exists) {
        return NextResponse.json(
          { error: 'Document not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        id: doc.id,
        ...doc.data()
      });
    }

    // Otherwise, get a paginated list of documents
    let query = adminDb.collection('Agent_Output').orderBy('timestamp', 'desc');

    // For Firestore, we need to use startAfter for pagination instead of offset
    // First page doesn't need a cursor
    if (page === 1) {
      // Get one more item than requested to determine if there are more results
      const querySnapshot = await query.limit(limit + 1).get();

      // If no documents were found, return empty results
      if (querySnapshot.empty) {
        return NextResponse.json({
          results: [],
          hasMore: false,
          page,
          limit,
          lastTimestamp: null
        });
      }

      // Check if there are more results
      const hasMore = querySnapshot.docs.length > limit;

      // Remove the extra item if there are more results
      const outputs = querySnapshot.docs.slice(0, limit).map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Store the last document as a cursor in the response
      // If there are no results or fewer than limit, lastDoc will be the last item
      const lastIndex = Math.min(querySnapshot.docs.length - 1, limit - 1);
      const lastDoc = lastIndex >= 0 ? querySnapshot.docs[lastIndex] : null;
      const lastTimestamp = lastDoc ? lastDoc.data().timestamp : null;

      return NextResponse.json({
        results: outputs,
        hasMore,
        page,
        limit,
        lastTimestamp: lastTimestamp ? JSON.stringify(lastTimestamp) : null
      });
    } else {
      // Get the lastTimestamp from the query parameters
      const lastTimestampStr = url.searchParams.get('lastTimestamp');

      if (!lastTimestampStr) {
        return NextResponse.json(
          { error: 'Missing lastTimestamp parameter for pagination' },
          { status: 400 }
        );
      }

      try {
        // Parse the lastTimestamp
        const lastTimestamp = JSON.parse(lastTimestampStr);

        // Get documents after the last timestamp
        const querySnapshot = await query
          .startAfter(lastTimestamp)
          .limit(limit + 1)
          .get();

        // If no documents were found, return empty results
        if (querySnapshot.empty) {
          return NextResponse.json({
            results: [],
            hasMore: false,
            page,
            limit,
            lastTimestamp: null
          });
        }

        // Check if there are more results
        const hasMore = querySnapshot.docs.length > limit;

        // Remove the extra item if there are more results
        const outputs = querySnapshot.docs.slice(0, limit).map(doc => ({
          id: doc.id,
          ...doc.data()
        }));

        // Store the last document as a cursor in the response
        // If there are no results, lastDoc will be null
        const lastIndex = querySnapshot.docs.length - 1;
        const lastDoc = lastIndex >= 0 ? querySnapshot.docs[lastIndex] : null;
        const newLastTimestamp = lastDoc ? lastDoc.data().timestamp : null;

        return NextResponse.json({
          results: outputs,
          hasMore,
          page,
          limit,
          lastTimestamp: newLastTimestamp ? JSON.stringify(newLastTimestamp) : null
        });
      } catch (error) {
        console.error('Error parsing lastTimestamp:', error);
        return NextResponse.json(
          { error: 'Invalid lastTimestamp parameter' },
          { status: 400 }
        );
      }
    }
  } catch (error) {
    console.error('Error retrieving agent outputs:', error);

    return NextResponse.json(
      {
        error: 'Failed to retrieve agent outputs',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
