/**
 * Strategic Task Utils Utility
 * 
 * Centralized utility functions for Strategic Director Agent task management.
 * This module contains general utility functions for task processing and analysis.
 */

// Type definitions (extracted from StrategicDirectorAgent)
export interface StrategicTask {
  id: string;
  title: string;
  description: string;
  category: string;
  priority: string;
  assignedTeam: string;
  status: string;
  specificRequirements: string[];
  deliverable: string;
  timeline: {
    estimatedDuration: string;
    startDate?: Date;
    dueDate?: Date;
  };
  dependencies?: string[];
  blockers?: string[];
  successCriteria: string[];
  resources?: {
    budget?: number;
    tools?: string[];
    personnel?: string[];
    documents?: string[];
  };
  metadata: {
    createdAt: Date;
    createdBy: string;
    updatedAt: Date;
    source: string;
    pmoId?: string;
    projectId?: string;
    requestId?: string;
  };
}

/**
 * Strategic Task Utils Class
 * Contains general utility functions for strategic task management
 */
export class StrategicTaskUtils {

  /**
   * Parse timeline string to weeks (e.g., "2 weeks" -> 2, "1 week" -> 1)
   */
  static parseTimelineToWeeks(timeline: string): number {
    const match = timeline.match(/(\d+)\s*weeks?/i);
    if (match) {
      return parseInt(match[1], 10);
    }

    // Handle days
    const dayMatch = timeline.match(/(\d+)\s*days?/i);
    if (dayMatch) {
      return Math.ceil(parseInt(dayMatch[1], 10) / 7); // Convert days to weeks
    }

    // Handle months
    const monthMatch = timeline.match(/(\d+)\s*months?/i);
    if (monthMatch) {
      return parseInt(monthMatch[1], 10) * 4; // Convert months to weeks
    }

    // Default to 2 weeks if parsing fails
    return 2;
  }

  /**
   * Enhanced deduplication with analytics - removes duplicate tasks based on title and assigned team
   */
  static deduplicateTasks(tasks: StrategicTask[]): StrategicTask[] {
    const seen = new Set<string>();
    const duplicates: string[] = [];

    const uniqueTasks = tasks.filter(task => {
      const key = `${task.title}-${task.assignedTeam}`;
      if (seen.has(key)) {
        duplicates.push(`"${task.title}" (${task.assignedTeam})`);
        return false;
      }
      seen.add(key);
      return true;
    });

    // Analytics logging
    if (duplicates.length > 0) {
      console.log(`StrategicTaskUtils: Removed ${duplicates.length} duplicate tasks:`);
      duplicates.forEach((duplicate, index) => {
        console.log(`  ${index + 1}. ${duplicate}`);
      });
    } else {
      console.log(`StrategicTaskUtils: No duplicate tasks found - all ${tasks.length} tasks are unique`);
    }

    return uniqueTasks;
  }

  /**
   * Calculate critical path for task dependencies
   */
  static calculateCriticalPath(tasks: StrategicTask[]): { criticalPath: string[]; totalDuration: number; } {
    console.log(`StrategicTaskUtils: Calculating critical path for ${tasks.length} tasks`);

    // Create a map of task dependencies
    const taskMap = new Map<string, StrategicTask>();
    const dependencyMap = new Map<string, string[]>();
    const inDegree = new Map<string, number>();

    // Initialize maps
    tasks.forEach(task => {
      taskMap.set(task.id, task);
      dependencyMap.set(task.id, task.dependencies || []);
      inDegree.set(task.id, (task.dependencies || []).length);
    });

    // Topological sort to find critical path
    const queue: string[] = [];
    const criticalPath: string[] = [];
    let totalDuration = 0;

    // Find tasks with no dependencies (starting points)
    tasks.forEach(task => {
      if ((task.dependencies || []).length === 0) {
        queue.push(task.id);
      }
    });

    // Process tasks in dependency order
    while (queue.length > 0) {
      const currentTaskId = queue.shift()!;
      const currentTask = taskMap.get(currentTaskId);
      
      if (currentTask) {
        criticalPath.push(currentTaskId);
        const taskDuration = this.parseTimelineToWeeks(currentTask.timeline.estimatedDuration);
        totalDuration += taskDuration;

        // Update dependent tasks
        tasks.forEach(task => {
          if (task.dependencies && task.dependencies.includes(currentTaskId)) {
            const newInDegree = (inDegree.get(task.id) || 0) - 1;
            inDegree.set(task.id, newInDegree);
            
            if (newInDegree === 0) {
              queue.push(task.id);
            }
          }
        });
      }
    }

    console.log(`StrategicTaskUtils: Critical path calculated with ${criticalPath.length} tasks and ${totalDuration} weeks total duration`);

    return {
      criticalPath,
      totalDuration
    };
  }

  /**
   * Extract model name from provider string (e.g., "openai/gpt-4o" -> "gpt-4o")
   */
  static extractModelName(modelNameOrProvider?: string): string | undefined {
    if (!modelNameOrProvider) return undefined;
    if (modelNameOrProvider.includes('/')) {
      return modelNameOrProvider.split('/')[1];
    }
    return modelNameOrProvider;
  }

  /**
   * Generate task ID with timestamp and random suffix
   */
  static generateTaskId(prefix: string = 'task'): string {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Calculate estimated end date based on start date and duration
   */
  static calculateEndDate(startDate: Date, duration: string): Date {
    const weeks = this.parseTimelineToWeeks(duration);
    return new Date(startDate.getTime() + (weeks * 7 * 24 * 60 * 60 * 1000));
  }

  /**
   * Group tasks by team assignment
   */
  static groupTasksByTeam(tasks: StrategicTask[]): Record<string, StrategicTask[]> {
    const groupedTasks: Record<string, StrategicTask[]> = {};

    tasks.forEach(task => {
      const team = task.assignedTeam;
      if (!groupedTasks[team]) {
        groupedTasks[team] = [];
      }
      groupedTasks[team].push(task);
    });

    return groupedTasks;
  }

  /**
   * Calculate team workload distribution
   */
  static calculateTeamWorkload(tasks: StrategicTask[]): Record<string, { taskCount: number; totalWeeks: number; averageWeeks: number; }> {
    const workload: Record<string, { taskCount: number; totalWeeks: number; averageWeeks: number; }> = {};

    tasks.forEach(task => {
      const team = task.assignedTeam;
      const weeks = this.parseTimelineToWeeks(task.timeline.estimatedDuration);

      if (!workload[team]) {
        workload[team] = { taskCount: 0, totalWeeks: 0, averageWeeks: 0 };
      }

      workload[team].taskCount++;
      workload[team].totalWeeks += weeks;
    });

    // Calculate averages
    Object.keys(workload).forEach(team => {
      workload[team].averageWeeks = workload[team].totalWeeks / workload[team].taskCount;
    });

    return workload;
  }

  /**
   * Validate task data structure
   */
  static validateTask(task: any): { isValid: boolean; errors: string[]; } {
    const errors: string[] = [];

    if (!task.id) errors.push('Task ID is required');
    if (!task.title) errors.push('Task title is required');
    if (!task.description) errors.push('Task description is required');
    if (!task.assignedTeam) errors.push('Assigned team is required');
    if (!task.timeline?.estimatedDuration) errors.push('Timeline duration is required');

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Sort tasks by priority (CRITICAL > HIGH > MEDIUM > LOW)
   */
  static sortTasksByPriority(tasks: StrategicTask[]): StrategicTask[] {
    const priorityOrder = { 'CRITICAL': 0, 'HIGH': 1, 'MEDIUM': 2, 'LOW': 3 };
    
    return [...tasks].sort((a, b) => {
      const aPriority = priorityOrder[a.priority as keyof typeof priorityOrder] ?? 4;
      const bPriority = priorityOrder[b.priority as keyof typeof priorityOrder] ?? 4;
      return aPriority - bPriority;
    });
  }

  /**
   * Filter tasks by status
   */
  static filterTasksByStatus(tasks: StrategicTask[], status: string): StrategicTask[] {
    return tasks.filter(task => task.status === status);
  }

  /**
   * Filter tasks by team
   */
  static filterTasksByTeam(tasks: StrategicTask[], team: string): StrategicTask[] {
    return tasks.filter(task => task.assignedTeam === team);
  }

  /**
   * Get task statistics
   */
  static getTaskStatistics(tasks: StrategicTask[]): {
    total: number;
    byStatus: Record<string, number>;
    byTeam: Record<string, number>;
    byPriority: Record<string, number>;
    totalEstimatedWeeks: number;
  } {
    const stats = {
      total: tasks.length,
      byStatus: {} as Record<string, number>,
      byTeam: {} as Record<string, number>,
      byPriority: {} as Record<string, number>,
      totalEstimatedWeeks: 0
    };

    tasks.forEach(task => {
      // Count by status
      stats.byStatus[task.status] = (stats.byStatus[task.status] || 0) + 1;
      
      // Count by team
      stats.byTeam[task.assignedTeam] = (stats.byTeam[task.assignedTeam] || 0) + 1;
      
      // Count by priority
      stats.byPriority[task.priority] = (stats.byPriority[task.priority] || 0) + 1;
      
      // Sum estimated weeks
      stats.totalEstimatedWeeks += this.parseTimelineToWeeks(task.timeline.estimatedDuration);
    });

    return stats;
  }
}
