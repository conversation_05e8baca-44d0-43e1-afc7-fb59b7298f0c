/**
 * Create PMO Tasks Agent
 *
 * This agent automates the creation of tasks for PMO projects by:
 * 1. Using QuestionAnswerAgent for enhanced contextual analysis and relevancy assessment
 * 2. Using PMO category context (generated from PMO Title) rather than reading documents
 * 3. Using OpenAI o3 to analyze and derive tasks from strategic context
 * 4. Using the createTasksTool to create the tasks in the system
 * 5. Providing relevancy scoring and confidence metrics for better task quality
 */

import { QueryDocumentsAgent } from '../../components/Agents/QueryDocumentsAgent';
import { QuestionAnswerAgent, QuestionAnswerResult } from '../../components/Agents/QuestionAnswerAgent';
import { createTasksTool, CreateTasksInput, CreateTasksResponse } from '../tools/createTasksTool';
import { TaskItem } from '../tools/generateTasksListTool';
import { chartTool, ChartGenerationResult, CHART_TYPES } from '../tools';
import { Project, Task } from 'admin/planner/types';
import { processWithOpenAI } from '../tools/openai-ai';

export interface CreatePMOTasksAgentOptions {
  includeExplanation?: boolean;
  streamResponse?: boolean;
  onStreamUpdate?: (update: CreatePMOTasksStreamUpdate) => void;
}

export interface CreatePMOTasksStreamUpdate {
  stage: 'reading-strategic-plan' | 'reading-requirements' | 'analyzing-documents' | 'generating-tasks' | 'creating-tasks' | 'visualizing-tasks' | 'complete';
  data?: any;
  message?: string;
}

export interface CreatePMOTasksAgentResult {
  success: boolean;
  strategicPlanContent: string;
  requirementsContent?: string;
  analysis: string;
  reasoning: string;
  tasks: Task[];
  creationResults: CreateTasksResponse;
  taskListVisualization?: ChartGenerationResult;
  pmoCategory?: string;
  contextAnalysis?: QuestionAnswerResult;
  relevancyScore?: number;
  error?: string;
}

export class CreatePMOTasksAgent {
  private options: CreatePMOTasksAgentOptions;
  private queryDocumentsAgent: QueryDocumentsAgent;
  private questionAnswerAgent: QuestionAnswerAgent;

  constructor(options: CreatePMOTasksAgentOptions = {}) {
    this.options = {
      includeExplanation: options.includeExplanation ?? true,
      streamResponse: options.streamResponse ?? false,
      onStreamUpdate: options.onStreamUpdate
    };

    // Initialize QueryDocumentsAgent
    this.queryDocumentsAgent = new QueryDocumentsAgent({
      maxResults: 10,
      defaultTemperature: 0.3,
      defaultMaxTokens: 4000,
      includeExplanation: true
    });

    // Initialize QuestionAnswerAgent for enhanced relevancy assessment
    this.questionAnswerAgent = new QuestionAnswerAgent({
      maxResults: 10,
      defaultTemperature: 0.3,
      defaultMaxTokens: 4000
    });
  }

  /**
   * Create tasks from PMO category context
   * @param projectId - ID of the project to create tasks for
   * @param project - Project details
   * @param pmoCategory - PMO category (generated from PMO Title) for contextual information
   * @param additionalContext - Optional additional context from UI inputs
   * @returns - Result of the task creation process
   */
  async createTasksFromStrategicPlan(
    projectId: string,
    project: Project,
    pmoCategory: string,
    additionalContext?: string
  ): Promise<CreatePMOTasksAgentResult> {
    try {
      if (!projectId || !project || !pmoCategory) {
        throw new Error("Project ID, project details, and PMO category are required");
      }

      console.log("CreatePMOTasksAgent: Starting task creation from PMO category context");

      // Step 1: Use QuestionAnswerAgent for enhanced contextual analysis
      this._streamUpdate('reading-strategic-plan', null, 'Analyzing PMO category context with enhanced relevancy...');
      console.log(`CreatePMOTasksAgent: Using QuestionAnswerAgent for PMO category: ${pmoCategory}`);

      const strategicPlanQuery = `
Based on the PMO category "${pmoCategory}", what are the key strategic planning elements I need to consider for task creation? Include:
- Strategic objectives and goals for this category
- Implementation timeline considerations
- Resource requirements typical for this category
- Key deliverables and milestones
- Success metrics and KPIs
- Risk assessment and mitigation strategies
- Team responsibilities and assignments
- PMO alignment and compliance requirements
${additionalContext ? `\n\nAdditional Context:\n${additionalContext}` : ''}
`;

      const contextAnalysis = await this.questionAnswerAgent.process({
        userRequest: strategicPlanQuery,
        userId: project.owner,
        category: pmoCategory,
        enabledTools: {
          internetSearch: false,
          calculator: false,
          calendar: false
        }
      });

      if (!contextAnalysis.success) {
        throw new Error(`Failed to analyze PMO category context: ${contextAnalysis.error}`);
      }

      const strategicPlanContent = contextAnalysis.summary || contextAnalysis.questions[0]?.answer || '';
      const relevancyScore = this._calculateRelevancyScore(contextAnalysis);

      console.log(`CreatePMOTasksAgent: PMO category context analyzed successfully (relevancy: ${relevancyScore})`);

      // Step 2: Query additional requirements context (if needed)
      let requirementsContent = '';
      if (additionalContext) {
        this._streamUpdate('reading-requirements', null, 'Analyzing additional requirements context...');
        console.log(`CreatePMOTasksAgent: Querying additional requirements context for PMO category: ${pmoCategory}`);

        const requirementsQuery = `
Based on the PMO category "${pmoCategory}" and additional context, provide requirements analysis including:
- Functional requirements
- Non-functional requirements
- Business requirements
- Technical specifications
- Constraints and assumptions
- Acceptance criteria
- PMO compliance requirements

Additional Context:
${additionalContext}
`;

        const requirementsResult = await this.queryDocumentsAgent.process({
          query: requirementsQuery,
          userId: project.owner,
          category: pmoCategory,
          useInternetSearch: false,
          model: 'gemini-2.5-pro-preview-05-06'
        });

        if (requirementsResult.success) {
          requirementsContent = requirementsResult.content;
          console.log("CreatePMOTasksAgent: Additional requirements context retrieved successfully");
        }
      }

      // Step 3: Analyze documents and derive tasks using OpenAI o3
      this._streamUpdate('analyzing-documents', {
        strategicPlanContent: strategicPlanContent.substring(0, 500) + '...',
        requirementsContent: requirementsContent.substring(0, 500) + '...'
      }, 'Analyzing documents with OpenAI o3...');

      console.log("CreatePMOTasksAgent: Analyzing documents with OpenAI o3");

      const analysisPrompt = `
You are a senior project management expert tasked with deriving specific, actionable tasks from PMO strategic planning documents.

PROJECT INFORMATION:
- Project Name: ${project.name}
- Project Description: ${project.description}
- Start Date: ${project.startDate.toISOString().split('T')[0]}
- End Date: ${project.endDate.toISOString().split('T')[0]}
- Categories: ${project.categories?.join(', ') || 'None'}

STRATEGIC PLAN CONTENT (from services/pmo/):
${strategicPlanContent}

${requirementsContent ? `
REQUIREMENTS ANALYSIS (from services/pmo/):
${requirementsContent}
` : ''}

PMO CONTEXT:
- Documents are sourced from services/pmo/ directory
- Tasks must align with PMO standards and compliance requirements
- Strategic plans follow PMO methodology and templates

TASK DERIVATION INSTRUCTIONS:
1. Analyze the strategic plan and identify all actionable items, deliverables, and milestones
2. Break down high-level objectives into specific, measurable tasks
3. Consider the requirements analysis to ensure technical and business needs are addressed
4. Create a chronological sequence of tasks that aligns with the project timeline
5. Assign realistic priorities based on dependencies and business impact
6. Ensure each task has clear acceptance criteria and deliverables

For each task, provide:
- Title (concise and action-oriented)
- Detailed description with acceptance criteria
- Priority (Critical, High, Medium, Low)
- Estimated start and due dates within the project timeframe
- Dependencies on other tasks
- Category (from project categories)
- Notes with additional context or requirements

Return your analysis and task list in the following JSON format:
{
  "analysis": "Your detailed analysis of the strategic plan and how tasks were derived",
  "reasoning": "Your reasoning for the task breakdown and prioritization",
  "tasks": [
    {
      "title": "Task Title",
      "description": "Detailed task description with acceptance criteria",
      "priority": "High",
      "startDate": "YYYY-MM-DD",
      "dueDate": "YYYY-MM-DD",
      "dependencies": ["Task Title 1", "Task Title 2"],
      "category": "Category Name",
      "notes": "Additional notes and context"
    }
  ]
}
`;

      const analysisResult = await processWithOpenAI({
        prompt: analysisPrompt,
        model: 'o3-2025-04-16',
        modelOptions: {
          temperature: 0.2,
          maxTokens: 8000,
        },
      });

      // Parse the analysis result
      const jsonMatch = analysisResult.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in OpenAI o3 analysis response');
      }

      const parsedAnalysis = JSON.parse(jsonMatch[0]);
      console.log(`CreatePMOTasksAgent: Analysis complete, derived ${parsedAnalysis.tasks.length} tasks`);

      // Step 4: Create tasks in the system using createTasksTool
      this._streamUpdate('creating-tasks', {
        analysis: parsedAnalysis.analysis,
        reasoning: parsedAnalysis.reasoning,
        tasks: parsedAnalysis.tasks
      }, 'Creating tasks in the system...');

      console.log("CreatePMOTasksAgent: Creating tasks in the system");

      const createTasksInput: CreateTasksInput = {
        projectId,
        tasks: parsedAnalysis.tasks
      };

      const createTasksResult = await createTasksTool.execute(createTasksInput);
      console.log(`CreatePMOTasksAgent: Created ${createTasksResult.results.filter(r => r.success).length} tasks successfully`);

      // Step 5: Generate a table visualization of the tasks
      this._streamUpdate('visualizing-tasks', null, 'Generating task list visualization...');
      console.log("CreatePMOTasksAgent: Generating task list visualization");

      const tasks = this._mapTaskItemsToTasks(parsedAnalysis.tasks, projectId);
      const taskListVisualization = await this._generateTaskListVisualization(project.name, tasks);

      console.log("CreatePMOTasksAgent: Task list visualization generated");

      // Step 6: Return the complete result
      const result: CreatePMOTasksAgentResult = {
        success: true,
        strategicPlanContent,
        requirementsContent: requirementsContent || undefined,
        analysis: parsedAnalysis.analysis,
        reasoning: parsedAnalysis.reasoning,
        tasks,
        creationResults: createTasksResult,
        taskListVisualization,
        pmoCategory,
        contextAnalysis,
        relevancyScore
      };

      this._streamUpdate('complete', result, 'PMO task creation complete!');
      return result;

    } catch (error) {
      console.error("CreatePMOTasksAgent: Error creating tasks:", error);

      return {
        success: false,
        strategicPlanContent: '',
        analysis: '',
        reasoning: '',
        tasks: [],
        creationResults: {
          results: [],
          summary: 'PMO task creation failed'
        },
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Stream update helper
   * @private
   */
  private _streamUpdate(stage: CreatePMOTasksStreamUpdate['stage'], data?: any, message?: string): void {
    if (this.options.streamResponse && this.options.onStreamUpdate) {
      this.options.onStreamUpdate({ stage, data, message });
    }
  }

  /**
   * Map TaskItems to Tasks
   * @private
   */
  private _mapTaskItemsToTasks(taskItems: TaskItem[], projectId: string): Task[] {
    return taskItems.map((task, index) => {
      const mappedTask: Task = {
        id: `pmo-task-${Date.now()}-${index}`,
        title: task.title,
        description: task.description,
        projectId,
        category: task.category,
        status: 'Not Started',
        startDate: new Date(task.startDate),
        dueDate: new Date(task.dueDate),
        assignedTo: ['<EMAIL>'], // Assign to Admin User
        priority: task.priority,
        dependencies: task.dependencies || [],
        notes: task.notes,
        createdBy: 'pmo-agent',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      return mappedTask;
    });
  }

  /**
   * Calculate relevancy score from QuestionAnswerResult
   * @private
   */
  private _calculateRelevancyScore(contextAnalysis: QuestionAnswerResult): number {
    if (!contextAnalysis.success) return 0;

    // Calculate average confidence from all Q&A pairs
    if (contextAnalysis.questions && contextAnalysis.questions.length > 0) {
      const totalConfidence = contextAnalysis.questions.reduce((sum, qa) => {
        return sum + (qa.confidence || 0.7); // Default confidence if not provided
      }, 0);
      return totalConfidence / contextAnalysis.questions.length;
    }

    // Default relevancy score for successful results without confidence data
    return 0.7;
  }

  /**
   * Generate task list visualization
   * @private
   */
  private async _generateTaskListVisualization(projectName: string, tasks: Task[]): Promise<ChartGenerationResult> {
    const chartPrompt = `
Create a comprehensive task management table for the project "${projectName}".

TASKS DATA:
${JSON.stringify(tasks, null, 2)}

The table should have the following columns:
- ID
- Title
- Priority (color-coded: Critical=Red, High=Orange, Medium=Yellow, Low=Green)
- Status (color-coded: Complete=Green, In Progress=Yellow, Reviewed=Blue, Not Started=Gray)
- Start Date
- Due Date
- Category
- Dependencies

Sort the tasks by start date.
Include a detailed explanation of the PMO task breakdown and strategic alignment.
`;

    return await chartTool.generateChart({
      prompt: chartPrompt,
      chartType: CHART_TYPES.TABLE,
      model: "o3-2025-04-16",
      provider: "openai"
    });
  }
}
