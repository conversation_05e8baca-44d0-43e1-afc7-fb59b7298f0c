/**
 * Test script for CreateProjectAgent and pmoProjectsTaskAgent
 * 
 * This test demonstrates the complete workflow:
 * 1. Creating projects from Strategic Director Agent outputs
 * 2. Extracting tasks using Groq deepseek LLM
 * 3. Creating tasks with ADMIN assignment and HIGH priority
 * 4. Updating PMO records with project IDs
 */

import { createProjectAgent } from '../createProjectAgent';
import { pmoProjectsTaskAgent } from '../pmoProjectsTaskAgent';

// Mock agent output data for testing
const mockAgentOutput = {
  id: 'test-agent-output-123',
  agentType: 'strategic-director',
  category: 'Marketing',
  result: {
    output: `
# Strategic Marketing Plan for Q1 2025

## Executive Summary
Based on market analysis and competitive research, we recommend implementing a comprehensive digital marketing strategy focused on brand awareness and lead generation.

## Key Initiatives

### 1. Brand Awareness Campaign
- Develop a multi-channel brand awareness campaign targeting millennials and Gen Z
- Create compelling video content for social media platforms
- Implement influencer partnerships to expand reach
- Launch targeted advertising campaigns on Facebook, Instagram, and TikTok

### 2. Content Marketing Strategy
- Establish a content calendar with weekly blog posts and social media content
- Create educational webinars and downloadable resources
- Develop case studies showcasing customer success stories
- Implement SEO optimization for all content

### 3. Lead Generation Program
- Design and implement lead magnets (ebooks, whitepapers, free tools)
- Set up automated email marketing sequences
- Create landing pages optimized for conversion
- Implement lead scoring and nurturing workflows

## Implementation Timeline
- Phase 1 (Weeks 1-4): Brand awareness campaign setup and content creation
- Phase 2 (Weeks 5-8): Lead generation program implementation
- Phase 3 (Weeks 9-12): Optimization and scaling based on performance data

## Expected Outcomes
- 40% increase in brand awareness metrics
- 25% increase in qualified leads
- 15% improvement in conversion rates
- ROI of 300% within 6 months
    `,
    thinking: `
The user is asking for a comprehensive marketing strategy. I need to analyze their current market position and provide actionable recommendations.

Key considerations:
1. Target audience analysis shows strong potential in younger demographics
2. Competitive landscape requires differentiated positioning
3. Budget constraints suggest focusing on digital channels first
4. Timeline needs to be realistic but aggressive

I'll structure this as a phased approach with clear deliverables and measurable outcomes.
    `
  },
  pmoMetadata: {
    teamName: 'Marketing',
    category: 'Strategic Planning'
  },
  createdAt: new Date(),
  updatedAt: new Date()
};

/**
 * Test the complete project creation workflow
 */
async function testCreateProjectWorkflow() {
  console.log('🚀 Starting CreateProjectAgent test workflow...\n');

  try {
    // Test 1: Create projects from agent output
    console.log('📋 Test 1: Creating projects from agent output...');
    
    const result = await createProjectAgent.createProjectsFromAgentOutput(
      'test-agent-output-123',
      '<EMAIL>',
      'test-pmo-record-123' // Optional PMO ID
    );

    if (result.success) {
      console.log('✅ Projects created successfully!');
      console.log(`   - Projects created: ${result.projectsCreated.length}`);
      console.log(`   - Total tasks created: ${result.totalTasksCreated}`);
      console.log(`   - PMO updated: ${result.pmoUpdated}`);
      
      result.projectsCreated.forEach((project, index) => {
        console.log(`   - Project ${index + 1}: ${project.projectName} (${project.tasksCreated} tasks)`);
      });
      
      if (result.analysis) {
        console.log(`   - Analysis: ${result.analysis.substring(0, 100)}...`);
      }
    } else {
      console.log('❌ Project creation failed:');
      console.log(`   - Error: ${result.error}`);
      if (result.analysis) {
        console.log(`   - Analysis: ${result.analysis}`);
      }
    }

    console.log('\n');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

/**
 * Test the task extraction functionality separately
 */
async function testTaskExtraction() {
  console.log('🔧 Test 2: Testing task extraction with pmoProjectsTaskAgent...\n');

  try {
    const taskResult = await pmoProjectsTaskAgent.createTasksFromAgentOutput(
      'test-agent-output-123',
      'test-project-123',
      'Test Marketing Campaign Project',
      'A comprehensive marketing campaign to increase brand awareness and generate leads'
    );

    if (taskResult.success) {
      console.log('✅ Tasks extracted and created successfully!');
      console.log(`   - Tasks created: ${taskResult.tasksCreated.length}`);
      console.log(`   - Total extracted: ${taskResult.totalTasksExtracted}`);
      
      taskResult.tasksCreated.forEach((task, index) => {
        console.log(`   - Task ${index + 1}: ${task.title}`);
      });
      
      if (taskResult.analysis) {
        console.log(`   - Analysis: ${taskResult.analysis.substring(0, 100)}...`);
      }
    } else {
      console.log('❌ Task extraction failed:');
      console.log(`   - Error: ${taskResult.error}`);
      if (taskResult.analysis) {
        console.log(`   - Analysis: ${taskResult.analysis}`);
      }
    }

    console.log('\n');

  } catch (error) {
    console.error('❌ Task extraction test failed:', error);
  }
}

/**
 * Test API endpoint simulation
 */
async function testAPIEndpoint() {
  console.log('🌐 Test 3: Simulating API endpoint call...\n');

  try {
    // Simulate the API request body
    const apiRequest = {
      requestId: 'test-agent-output-123',
      pmoId: 'test-pmo-record-123',
      userId: '<EMAIL>'
    };

    console.log('📤 API Request:', JSON.stringify(apiRequest, null, 2));

    // This would be the actual API call in a real scenario
    const result = await createProjectAgent.createProjectsFromAgentOutput(
      apiRequest.requestId,
      apiRequest.userId,
      apiRequest.pmoId
    );

    // Simulate API response
    const apiResponse = {
      success: result.success,
      message: result.success 
        ? `Successfully created ${result.projectsCreated.length} projects with ${result.totalTasksCreated} tasks`
        : 'Failed to create projects from agent output',
      data: result.success ? {
        projectsCreated: result.projectsCreated,
        totalProjects: result.projectsCreated.length,
        totalTasksCreated: result.totalTasksCreated,
        pmoUpdated: result.pmoUpdated,
        analysis: result.analysis,
        requestId: apiRequest.requestId,
        pmoId: apiRequest.pmoId,
        userId: apiRequest.userId
      } : {
        error: result.error,
        analysis: result.analysis
      }
    };

    console.log('📥 API Response:', JSON.stringify(apiResponse, null, 2));

  } catch (error) {
    console.error('❌ API endpoint test failed:', error);
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🧪 CreateProjectAgent & pmoProjectsTaskAgent Test Suite');
  console.log('=' .repeat(60));
  console.log('');

  // Note: These tests would require actual Firebase setup and Groq API key
  console.log('⚠️  Note: These tests require:');
  console.log('   - Firebase Admin SDK configuration');
  console.log('   - Groq API key in environment variables');
  console.log('   - Agent_Output document in Firebase');
  console.log('   - Proper authentication setup');
  console.log('');

  await testCreateProjectWorkflow();
  await testTaskExtraction();
  await testAPIEndpoint();

  console.log('🏁 Test suite completed!');
  console.log('');
  console.log('📝 Summary:');
  console.log('   - CreateProjectAgent: Extracts projects from agent outputs using Groq');
  console.log('   - pmoProjectsTaskAgent: Extracts tasks from agent outputs using Groq');
  console.log('   - All tasks assigned to ADMIN user (<EMAIL>) with HIGH priority');
  console.log('   - PMO records updated with project IDs (array support)');
  console.log('   - API endpoint available at /api/create-projects-from-agent-output');
}

// Export for use in other test files
export {
  testCreateProjectWorkflow,
  testTaskExtraction,
  testAPIEndpoint,
  runAllTests,
  mockAgentOutput
};

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}
