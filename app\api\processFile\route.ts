import { NextRequest, NextResponse } from "next/server";
import { setDoc, doc as firestoreDoc } from "firebase/firestore";
import { db } from "../../../components/firebase";
import { OpenAIEmbeddings } from "@langchain/openai";
import { getPineconeIndex } from "lib/pinecone-client";
import { FirestoreStore } from "lib/FirestoreStore";
import { processDocument } from "components/DocViewer/documentProcessors";
import { createGroqClient } from "lib/llms/groq";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/authOptions";

// Model constant
const GROQ_VISION_MODEL = process.env.GROQ_VISION_MODEL || "meta-llama/llama-4-maverick-17b-128e-instruct";

interface Document {
  pageContent: string;
  metadata: Record<string, any>;
}

function cleanMetadata(metadata: any): Record<string, any> {
  const cleaned: Record<string, any> = {};

  for (const [key, value] of Object.entries(metadata)) {
    if (value !== undefined && value !== null) {
      cleaned[key] = value;
    } else {
      switch (key) {
        case 'sectionTitle':
          cleaned[key] = 'No Title';
          break;
        case 'questions':
          cleaned[key] = [];
          break;
        case 'is_summary':
          cleaned[key] = false;
          break;
        case 'content':
          cleaned[key] = '';
          break;
        default:
          cleaned[key] = '';
      }
    }
  }

  return cleaned;
}

function createErrorMetadata(error: any, docInfo: {
  docId: string;
  fileName: string;
  fileType: string;
  category?: string;
  chunkId?: string;
}): Record<string, any> {
  return cleanMetadata({
    documentId: docInfo.docId,
    document_title: docInfo.fileName,
    file_type: docInfo.fileType,
    category: docInfo.category || 'Uncategorized',
    chunk_id: docInfo.chunkId || docInfo.docId,
    error_message: error instanceof Error ? error.message : 'Unknown error',
    error_details: error.toString(),
    created_at: new Date().toISOString(),
    sectionTitle: 'Processing Error',
    status: 'failed',
    is_error: true,
    processing_stage: 'document_processing'
  });
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  console.log(`[PROCESS FILE] Starting document processing`);

  // Get server session and validate
  const session = await getServerSession(authOptions);
  console.log(`[PROCESS FILE] Session user: ${session?.user?.email || 'No user found'}`);

  // Clone the request to read the body multiple times
  const clonedReq = req.clone();

  // Get request body to check for userEmail
  let requestBody;
  try {
    requestBody = await clonedReq.json();
    console.log(`[PROCESS FILE] Request body received with keys: ${Object.keys(requestBody).join(', ')}`);
  } catch (error) {
    console.error(`[PROCESS FILE] Error parsing request body:`, error);
    return NextResponse.json(
      { success: false, error: "Invalid request body" },
      { status: 400 }
    );
  }

  // Check if we have a session or userEmail in the request body
  const userEmail = session?.user?.email || requestBody.userEmail;
  console.log(`[PROCESS FILE] Using user email: ${userEmail || 'None'}`);

  if (!userEmail) {
    console.log(`[PROCESS FILE] Unauthorized - No valid session or user email`);
    return NextResponse.json(
      { success: false, error: "Unauthorized - No valid session or user email" },
      { status: 401 }
    );
  }

  console.log(`[PROCESS FILE] User authorized: ${userEmail}`);

  // Initialize Groq client with the authorized user's email
  const groq = createGroqClient({ userEmail: userEmail });

  // Define processImageWithGroq with access to groq client
  const processImageWithGroq = async (imageUrl: string): Promise<{ analysis: string; sectionTitle: string }> => {
    try {
      const chatCompletion = await groq.chat.completions.create({
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: `IMAGE TEXT CAPTURE:

MAIN TEXT:
[Verbatim capture of headlines, titles, main body text]

CHART/GRAPH TEXT:
- Axes labels: [x-axis, y-axis labels]
- Values/Data points: [numerical values, labels]
- Legend text: [categories, keys]
- Annotations: [notes, callouts]

SUPPLEMENTARY TEXT:
- Captions/Subtitles: [verbatim]
- Watermarks/Credits: [verbatim]
- UI elements: [buttons, menu items]

TITLE: (2-3 words capturing essence)

CORE ELEMENTS:
- Composition: Layout, focal points
- Visuals: Colors, lighting, quality
- Content: Main subjects, setting
- Impact: Mood, context

If people present:
- Appearance, expressions, interactions

TEXT PROPERTIES:
- Typography styles/hierarchy
- Text placement/flow
- Legibility/contrast`
              },
              {
                type: "image_url",
                image_url: {
                  url: imageUrl
                }
              }
            ]
          }
        ],
        model: GROQ_VISION_MODEL,
        temperature: 0.7,
        max_tokens: 1024,
      });

      if (!chatCompletion?.choices?.[0]?.message?.content) {
        throw new Error("Invalid response format from Groq API");
      }

      const response = chatCompletion.choices[0].message.content;

      const titleMatch = response.match(/TITLE:\s*(.*?)\s*\n/i);
      const analysisMatch = response.match(/ANALYSIS:\s*([\s\S]*)/i);

      const sectionTitle = titleMatch?.[1] || "Image Analysis";
      const analysis = analysisMatch?.[1]?.trim() || response.trim();

      return {
        analysis,
        sectionTitle
      };
    } catch (error) {
      console.error("Error processing image with Groq:", error);
      if (error instanceof Error) {
        throw new Error(`Image processing failed: ${error.message}`);
      }
      throw new Error("Image processing failed with unknown error");
    }
  };

  let docId = '';
  let userId = '';
  let fileName = '';
  let fileType = '';
  let category = 'Uncategorized';
  let fileUrl = '';
  let isImage = false;
  let projectId = ''; // Add projectId

  try {
    // Use the already parsed request body
    ({ docId, userId, category = "Uncategorized", fileName, fileType, fileUrl, isImage, projectId } = requestBody);

    if (!docId || !userId || !fileName || !fileType || !fileUrl) {
      throw new Error("Missing required parameters");
    }

    console.log(`[PROCESS FILE] Processing document: ${fileName} (${fileType}) for project: ${projectId || 'None'}`);
    console.log(`[PROCESS FILE] Document ID: ${docId}, User ID: ${userId}, Category: ${category}`);
    console.log(`[PROCESS FILE] Is image: ${isImage}`);
    console.log(`[PROCESS FILE] File URL: ${fileUrl.substring(0, 50)}...`);

    console.log(`[PROCESS FILE] Initializing OpenAI embeddings`);
    const embeddings = new OpenAIEmbeddings({
      apiKey: process.env.OPENAI_API_KEY!,
    });

    console.log(`[PROCESS FILE] Initializing Pinecone`);
    console.log(`[PROCESS FILE] Using fixed Pinecone index: 'ikedia'`);

    // Get the Pinecone index using our helper function
    const pineconeIndex = getPineconeIndex();
    console.log(`[PROCESS FILE] Retrieved Pinecone index`);

    const byteCollection = `users/${userId}/byteStoreCollection`;
    console.log(`[PROCESS FILE] Using Firestore collection: ${byteCollection}`);
    const firestoreStore = new FirestoreStore({ collectionPath: byteCollection });

    let processedContent;
    try {
      if (isImage) {
        const { analysis, sectionTitle } = await processImageWithGroq(fileUrl);
        processedContent = [{
          pageContent: analysis,
          metadata: {
            doc_id: docId,
            chunk_id: `${docId}_1`,
            sectionTitle: sectionTitle,
            questions: [],
            is_summary: true
          }
        }];
      } else {
        const fileResponse = await fetch(fileUrl);
        if (!fileResponse.ok) {
          throw new Error(`Failed to fetch file: ${fileResponse.statusText}`);
        }

        const fileBlob = await fileResponse.blob();
        const file = new File([fileBlob], fileName, { type: fileType });
        // Assuming the missing arguments are userId, category, and an additional parameter not specified in the context
        // Adding placeholders for the two known missing arguments and a generic 'additionalParameter' for the unspecified one
        const additionalParameter = ''; // Placeholder for the actual additional parameter needed
        const CHUNK_SIZE = 1500; // Example value, adjust as needed
        const CHUNK_OVERLAP = 200; // Example value, adjust as needed
        processedContent = await processDocument(file, docId, fileType, fileName, userId, category, additionalParameter, CHUNK_SIZE, CHUNK_OVERLAP);
      }

      if (!processedContent?.length) {
        throw new Error('No content was extracted from the document');
      }

      // Store all chunks in byteStoreCollection
      const byteStoreData: [string, Document][] = processedContent.map(doc => [
        doc.metadata.chunk_id,
        {
          pageContent: doc.pageContent,
          metadata: cleanMetadata({
            ...doc.metadata,
            document_title: fileName,
            category: category,
            file_type: fileType,
            is_image: isImage,
            fileUrl: fileUrl,
            projectId: projectId, // Add projectId to metadata
            processed_at: new Date().toISOString()
          })
        }
      ]);

      await firestoreStore.mset(byteStoreData);

      // Process and embed each chunk
      console.log(`[PROCESS FILE] Processing ${processedContent.length} content chunks`);
      for (const doc of processedContent) {
        try {
          console.log(`[PROCESS FILE] Processing chunk: ${doc.metadata.chunk_id}`);
          console.log(`[PROCESS FILE] Generating embedding for chunk content`);
          const embedding = await embeddings.embedQuery(doc.pageContent);
          console.log(`[PROCESS FILE] Embedding generated with length: ${embedding.length}`);

          const metadataForPinecone = cleanMetadata({
            content: doc.pageContent,
            doc_id: doc.metadata.doc_id,
            chunk_id: doc.metadata.chunk_id,
            document_title: fileName,
            category: category,
            file_type: fileType,
            is_image: isImage,
            projectId: projectId, // Add projectId to Pinecone metadata
            sectionTitle: doc.metadata.sectionTitle,
            questions: doc.metadata.questions,
            is_summary: doc.metadata.is_summary
          });
          console.log(`[PROCESS FILE] Prepared metadata for Pinecone with projectId: ${projectId}`);

          console.log(`[PROCESS FILE] Upserting to Pinecone index: 'ikedia', namespace: ${docId}`);
          try {
            console.log(`[PROCESS FILE] Upserting to Pinecone with namespace: ${docId}`);
            await pineconeIndex.namespace(docId).upsert([
              {
                id: doc.metadata.chunk_id,
                values: embedding,
                metadata: metadataForPinecone,
              },
            ]);
            console.log(`[PROCESS FILE] Successfully processed and stored chunk: ${doc.metadata.chunk_id}`);
          } catch (upsertError) {
            console.error(`[PROCESS FILE] Error upserting to Pinecone:`, upsertError);
            console.log(`[PROCESS FILE] Trying alternative upsert approach without namespace`);

            // Try alternative approach without namespace
            try {
              // Add namespace to metadata instead
              const metadataWithNamespace = {
                ...metadataForPinecone,
                namespace: docId
              };

              await pineconeIndex.upsert([
                {
                  id: doc.metadata.chunk_id,
                  values: embedding,
                  metadata: metadataWithNamespace,
                },
              ]);
              console.log(`[PROCESS FILE] Successfully stored chunk using alternative approach: ${doc.metadata.chunk_id}`);
            } catch (alternativeError) {
              console.error(`[PROCESS FILE] Alternative upsert approach also failed:`, alternativeError);
              // Continue processing other chunks
            }
          }
        } catch (chunkError: any) {
          console.error(`[PROCESS FILE] Error processing chunk ${doc.metadata.chunk_id}:`, chunkError);

          const errorMetadata = createErrorMetadata(chunkError, {
            docId,
            fileName,
            fileType,
            category,
            chunkId: doc.metadata.chunk_id
          });

          await setDoc(
            firestoreDoc(db, "users", userId, "MetadataFallback", doc.metadata.chunk_id),
            errorMetadata
          );
        }
      }

      console.log(`[PROCESS FILE] Document processing completed successfully`);
      return NextResponse.json({ success: true });

    } catch (processingError: any) {
      console.error("[PROCESS FILE] Error in document processing:", processingError);

      const errorMetadata = createErrorMetadata(processingError, {
        docId,
        fileName,
        fileType,
        category
      });

      console.log(`[PROCESS FILE] Saving error metadata to Firestore`);
      await setDoc(
        firestoreDoc(db, "users", userId, "MetadataFallback", docId),
        errorMetadata
      );
      console.log(`[PROCESS FILE] Error metadata saved`);

      throw processingError;
    }

  } catch (error: any) {
    console.error("[PROCESS FILE] Error processing file:", error);

    if (docId && userId) {
      try {
        console.log(`[PROCESS FILE] Creating error metadata for docId: ${docId}`);
        const errorMetadata = createErrorMetadata(error, {
          docId,
          fileName: fileName || 'Unknown File',
          fileType: fileType || 'Unknown Type',
          category
        });

        console.log(`[PROCESS FILE] Saving error metadata to Firestore`);
        await setDoc(
          firestoreDoc(db, "users", userId, "MetadataFallback", docId),
          errorMetadata
        );
        console.log(`[PROCESS FILE] Error metadata saved`);
      } catch (metadataError) {
        console.error("[PROCESS FILE] Failed to save error metadata:", metadataError);
      }
    } else {
      console.log(`[PROCESS FILE] Missing docId or userId, cannot save error metadata`);
    }

    const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
    console.log(`[PROCESS FILE] Returning error response: ${errorMessage}`);
    return NextResponse.json(
      {
        success: false,
        error: errorMessage
      },
      { status: 500 }
    );
  }
}

export async function OPTIONS(_req: NextRequest): Promise<NextResponse> {
  return NextResponse.json({}, { status: 200 });
}
